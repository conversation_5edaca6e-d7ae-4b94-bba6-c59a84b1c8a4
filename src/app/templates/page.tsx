"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Eye, ShoppingCart, IndianRupee, Star, Heart, Download, ExternalLink, Zap, Award } from "lucide-react"
import { createClient } from "@/lib/supabase/client"
import { Database } from "@/lib/database.types"
import { RazorpayButton } from "@/components/payment/razorpay-button"
import { toast } from "sonner"
import { useRouter, usePathname } from "next/navigation"

type Template = Database['public']['Tables']['templates']['Row']

export default function TemplatesPage() {
  const [templates, setTemplates] = useState<Template[]>([])
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [sortBy, setSortBy] = useState("title")
  const [categories, setCategories] = useState<string[]>(["All"])
  const [isFetching, setIsFetching] = useState(false)

  const supabase = createClient()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    let timeoutId: NodeJS.Timeout

    const debouncedFetch = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        fetchTemplates()
      }, 100) // 100ms debounce
    }

    const handlePageShow = (event: PageTransitionEvent) => {
      if (event.persisted) {
        console.log('Page restored from bfcache, refetching templates...')
        setLoading(true)
        debouncedFetch()
      }
    };

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !isFetching) {
        console.log('Page became visible, refetching templates...')
        debouncedFetch()
      }
    };

    // Initial data fetch on mount
    fetchTemplates()

    // Add event listeners
    window.addEventListener('pageshow', handlePageShow);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Listen for auth state changes to refetch data when user logs in/out
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        console.log('Auth state change in templates page:', event)
        if (event === 'SIGNED_IN' || event === 'SIGNED_OUT') {
          debouncedFetch()
        }
      }
    )

    return () => {
      clearTimeout(timeoutId)
      window.removeEventListener('pageshow', handlePageShow);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      subscription.unsubscribe()
    };
  }, []) // Remove pathname dependency to prevent unnecessary re-runs

  useEffect(() => {
    filterAndSortTemplates()
  }, [templates, searchTerm, selectedCategory, sortBy])

  const fetchTemplates = useCallback(async (force = false) => {
    // Prevent multiple simultaneous fetches
    if (isFetching && !force) {
      console.log('Already fetching templates, skipping...')
      return
    }

    try {
      setIsFetching(true)
      console.log('Fetching templates... Loading state:', loading, 'IsFetching:', isFetching)

      const { data, error } = await supabase
        .from('templates')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Supabase error:', error)
        throw error
      }

      console.log('Templates fetched successfully:', data?.length || 0)
      console.log('Setting templates and clearing loading state...')
      setTemplates(data || [])

      // Extract unique categories
      const uniqueCategories = ["All", ...new Set(data?.map(t => t.category) || [])]
      setCategories(uniqueCategories)

      console.log('Templates state updated, loading should be false now')
    } catch (error) {
      console.error('Error fetching templates:', error)
      toast.error('Failed to load templates. Please try refreshing the page.')
      // Don't clear existing templates on error, just show the error
    } finally {
      console.log('Finally block: setting loading to false')
      setLoading(false)
      setIsFetching(false)
    }
  }, [isFetching, loading, supabase])

  const filterAndSortTemplates = () => {
    let filtered = templates

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(template =>
        template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by category
    if (selectedCategory !== "All") {
      filtered = filtered.filter(template => template.category === selectedCategory)
    }

    // Sort templates
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "price-low":
          return a.price - b.price
        case "price-high":
          return b.price - a.price
        case "title":
        default:
          return a.title.localeCompare(b.title)
      }
    })

    setFilteredTemplates(filtered)
  }

  const handlePurchaseSuccess = (templateId: string) => {
    toast.success('Template purchased successfully!')
    router.push('/success')
  }

  const handlePurchaseError = (error: any) => {
    toast.error('Payment failed. Please try again.')
    console.error('Purchase error:', error)
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Templates</h1>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-muted border-t-primary rounded-full animate-spin" />
            <p className="text-muted-foreground">
              Loading templates... {isFetching ? '(Fetching data)' : '(Processing)'}
            </p>
          </div>
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <div className="aspect-video bg-muted animate-pulse" />
              <CardHeader>
                <div className="h-4 bg-muted animate-pulse rounded" />
                <div className="h-3 bg-muted animate-pulse rounded w-3/4" />
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    )
  }
  return (
    <div className="space-y-8">
      {/* Enhanced Header */}
      <div className="text-center space-y-4 py-8">
        <div className="space-y-2">
          <Badge variant="secondary" className="mb-4">
            <Award className="h-4 w-4 mr-2" />
            50+ Premium Templates
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Premium Templates
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Choose from our collection of professionally designed, responsive templates.
            Each template is crafted with modern design principles and best practices.
          </p>
        </div>

        {/* Stats */}
        <div className="flex justify-center items-center gap-8 pt-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">50+</div>
            <div className="text-sm text-muted-foreground">Templates</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">4.9★</div>
            <div className="text-sm text-muted-foreground">Rating</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">10K+</div>
            <div className="text-sm text-muted-foreground">Downloads</div>
          </div>
        </div>
      </div>

      {/* Enhanced Search and Filter */}
      <div className="bg-muted/30 rounded-2xl p-6 space-y-4">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search templates by name or description..."
              className="pl-10 h-12 text-lg"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-48 h-12">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="title">Sort by Title</SelectItem>
              <SelectItem value="price-low">Price: Low to High</SelectItem>
              <SelectItem value="price-high">Price: High to Low</SelectItem>
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="popular">Most Popular</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Category Filter */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">Categories</h3>
          <div className="flex gap-2 flex-wrap">
            {categories.map((category) => (
              <Button
                key={category}
                variant={category === selectedCategory ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="rounded-full"
              >
                {category}
                {category !== "All" && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {templates.filter(t => t.category === category).length}
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        </div>

        {/* Results Count */}
        <div className="flex justify-between items-center text-sm text-muted-foreground">
          <span>
            Showing {filteredTemplates.length} of {templates.length} templates
            {isFetching && <span className="ml-2 text-blue-600">(Fetching...)</span>}
          </span>
          <div className="flex gap-2">
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchTerm("")}
                className="text-xs"
              >
                Clear search
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                console.log('Debug: Force refresh templates')
                setLoading(true)
                fetchTemplates(true)
              }}
              className="text-xs"
            >
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Templates Grid */}
      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="group overflow-hidden hover:shadow-2xl transition-all duration-300 border-0 shadow-lg">
            {/* Image Container with Overlay */}
            <div className="aspect-video bg-gradient-to-br from-blue-50 to-purple-50 relative overflow-hidden">
              {template.preview_image ? (
                <img
                  src={template.preview_image}
                  alt={template.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              ) : (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center space-y-2">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                      <Zap className="h-8 w-8 text-blue-600" />
                    </div>
                    <p className="text-muted-foreground font-medium">Template Preview</p>
                  </div>
                </div>
              )}

              {/* Overlay with Quick Actions */}
              <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-3">
                <Button
                  size="sm"
                  variant="secondary"
                  className="bg-white/90 hover:bg-white text-black border-0"
                  onClick={() => window.open(template.preview_url || '/customize', '_blank')}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  className="bg-white/90 hover:bg-white text-black border-0"
                  onClick={() => router.push('/customize')}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Customize
                </Button>
              </div>

              {/* Category Badge */}
              <div className="absolute top-3 left-3">
                <Badge variant="secondary" className="bg-white/90 text-black border-0">
                  {template.category}
                </Badge>
              </div>

              {/* Favorite Button */}
              <div className="absolute top-3 right-3">
                <Button
                  size="sm"
                  variant="secondary"
                  className="w-8 h-8 p-0 bg-white/90 hover:bg-white text-black border-0 rounded-full"
                >
                  <Heart className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Card Content */}
            <CardHeader className="pb-3">
              <div className="space-y-2">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-xl font-bold group-hover:text-blue-600 transition-colors">
                    {template.title}
                  </CardTitle>
                  <div className="flex items-center gap-1 text-yellow-500">
                    <Star className="h-4 w-4 fill-current" />
                    <span className="text-sm font-medium">4.9</span>
                  </div>
                </div>
                <CardDescription className="text-sm leading-relaxed">
                  {template.description}
                </CardDescription>
              </div>
            </CardHeader>

            <CardContent className="pt-0">
              {/* Features */}
              <div className="flex flex-wrap gap-1 mb-4">
                <Badge variant="outline" className="text-xs">Responsive</Badge>
                <Badge variant="outline" className="text-xs">Modern</Badge>
                <Badge variant="outline" className="text-xs">Fast</Badge>
              </div>

              {/* Price and Actions */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    <IndianRupee className="h-5 w-5 text-green-600" />
                    <span className="text-3xl font-bold text-green-600">₹{template.price}</span>
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-muted-foreground line-through">₹{Math.round(template.price * 1.5)}</div>
                    <div className="text-xs text-green-600 font-medium">33% OFF</div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    className="w-full group/btn hover:bg-blue-50 hover:border-blue-200 hover:text-blue-700 transition-all"
                    onClick={() => window.open(template.preview_url || '/customize', '_blank')}
                  >
                    <Eye className="h-4 w-4 mr-2 group-hover/btn:scale-110 transition-transform" />
                    Preview
                  </Button>

                  <RazorpayButton
                    amount={template.price}
                    templateId={template.id}
                    description={`Purchase ${template.title}`}
                    onSuccess={(response) => handlePurchaseSuccess(template.id)}
                    onError={handlePurchaseError}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 font-medium group/btn"
                  >
                    <ShoppingCart className="h-4 w-4 mr-2 group-hover/btn:scale-110 transition-transform" />
                    Buy Now
                  </RazorpayButton>
                </div>

                {/* Quick Info */}
                <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
                  <span className="flex items-center gap-1">
                    <Download className="h-3 w-3" />
                    1.2K downloads
                  </span>
                  <span>Updated recently</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTemplates.length === 0 && !loading && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No templates found matching your criteria.</p>
        </div>
      )}
    </div>
  )
}
