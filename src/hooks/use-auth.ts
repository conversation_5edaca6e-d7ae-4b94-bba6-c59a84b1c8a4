"use client"

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { User } from '@supabase/supabase-js'

interface UserProfile {
  id: string;
  full_name?: string;
  role?: string;
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [initialized, setInitialized] = useState(false)

  const supabase = createClient()

  const loadUserProfile = useCallback(async (user: User) => {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (error && error.code !== 'PGRST116') {
        console.error('Error loading profile:', error)
        throw error
      }

      setProfile(profile)
    } catch (error) {
      console.error('Error loading profile:', error)
    }
  }, [supabase])

  const checkUser = useCallback(async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error) {
        console.error('Error checking user:', error)
        throw error
      }

      if (user) {
        setUser(user)
        await loadUserProfile(user)
      } else {
        setUser(null)
        setProfile(null)
      }
    } catch (error) {
      console.error('Error in checkUser:', error)
      setUser(null)
      setProfile(null)
    } finally {
      setLoading(false)
      setInitialized(true)
    }
  }, [supabase, loadUserProfile])

  useEffect(() => {
    // Only run once on mount
    if (!initialized) {
      checkUser()
    }

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state change:', event)
        
        if (event === 'SIGNED_IN' && session?.user) {
          setLoading(true)
          setUser(session.user)
          await loadUserProfile(session.user)
          setLoading(false)
        } else if (event === 'SIGNED_OUT') {
          setUser(null)
          setProfile(null)
          setLoading(false)
        } else if (event === 'TOKEN_REFRESHED' && session?.user) {
          setUser(session.user)
          // Don't set loading for token refresh
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [supabase.auth, checkUser, loadUserProfile, initialized])

  const signOut = useCallback(async () => {
    try {
      setLoading(true)
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      
      setUser(null)
      setProfile(null)
    } catch (error) {
      console.error('Error signing out:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }, [supabase])

  return {
    user,
    profile,
    loading,
    initialized,
    signOut,
    refetch: checkUser
  }
}
