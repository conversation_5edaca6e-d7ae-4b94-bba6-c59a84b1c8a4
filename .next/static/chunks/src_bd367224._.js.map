{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Slider({\n  className,\n  defaultValue,\n  value,\n  min = 0,\n  max = 100,\n  ...props\n}: React.ComponentProps<typeof SliderPrimitive.Root>) {\n  const _values = React.useMemo(\n    () =>\n      Array.isArray(value)\n        ? value\n        : Array.isArray(defaultValue)\n          ? defaultValue\n          : [min, max],\n    [value, defaultValue, min, max]\n  )\n\n  return (\n    <SliderPrimitive.Root\n      data-slot=\"slider\"\n      defaultValue={defaultValue}\n      value={value}\n      min={min}\n      max={max}\n      className={cn(\n        \"relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col\",\n        className\n      )}\n      {...props}\n    >\n      <SliderPrimitive.Track\n        data-slot=\"slider-track\"\n        className={cn(\n          \"bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5\"\n        )}\n      >\n        <SliderPrimitive.Range\n          data-slot=\"slider-range\"\n          className={cn(\n            \"bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full\"\n          )}\n        />\n      </SliderPrimitive.Track>\n      {Array.from({ length: _values.length }, (_, index) => (\n        <SliderPrimitive.Thumb\n          data-slot=\"slider-thumb\"\n          key={index}\n          className=\"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50\"\n        />\n      ))}\n    </SliderPrimitive.Root>\n  )\n}\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;;;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,YAAY,EACZ,KAAK,EACL,MAAM,CAAC,EACP,MAAM,GAAG,EACT,GAAG,OAC+C;;IAClD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;mCAC1B,IACE,MAAM,OAAO,CAAC,SACV,QACA,MAAM,OAAO,CAAC,gBACZ,eACA;gBAAC;gBAAK;aAAI;kCAClB;QAAC;QAAO;QAAc;QAAK;KAAI;IAGjC,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,cAAc;QACd,OAAO;QACP,KAAK;QACL,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uOACA;QAED,GAAG,KAAK;;0BAET,6LAAC,qKAAA,CAAA,QAAqB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,6LAAC,qKAAA,CAAA,QAAqB;oBACpB,aAAU;oBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;YAIL,MAAM,IAAI,CAAC;gBAAE,QAAQ,QAAQ,MAAM;YAAC,GAAG,CAAC,GAAG,sBAC1C,6LAAC,qKAAA,CAAA,QAAqB;oBACpB,aAAU;oBAEV,WAAU;mBADL;;;;;;;;;;;AAMf;GArDS;KAAA", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 626, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/app/customize/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Label } from \"@/components/ui/label\"\nimport { Input } from \"@/components/ui/input\"\nimport { Textarea } from \"@/components/ui/textarea\"\nimport { Slider } from \"@/components/ui/slider\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\"\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\"\nimport { createClient } from \"@/lib/supabase/client\"\nimport { getUser } from \"@/lib/auth\"\nimport { toast } from \"sonner\"\nimport {\n  Save,\n  Eye,\n  Mail,\n  Palette,\n  Type,\n  Layout,\n  Settings,\n  Globe,\n  Smartphone,\n  Monitor,\n  Tablet,\n  Moon,\n  Sun,\n  Zap,\n  Image,\n  Code,\n  Search,\n  Facebook,\n  Twitter,\n  Instagram,\n  Linkedin,\n  Youtube,\n  Phone,\n  MapPin,\n  Download,\n  Upload,\n  Trash2,\n  <PERSON><PERSON>,\n  <PERSON>fresh<PERSON><PERSON>\n} from \"lucide-react\"\n\n// Enhanced Configuration Options\nconst navbarStyles = [\n  { id: \"modern\", name: \"Modern\", description: \"Clean and minimal navigation\" },\n  { id: \"classic\", name: \"Classic\", description: \"Traditional horizontal menu\" },\n  { id: \"sidebar\", name: \"Sidebar\", description: \"Vertical side navigation\" },\n  { id: \"floating\", name: \"Floating\", description: \"Floating navigation bar\" },\n  { id: \"sticky\", name: \"Sticky\", description: \"Fixed navigation on scroll\" },\n  { id: \"transparent\", name: \"Transparent\", description: \"Transparent overlay navigation\" },\n  { id: \"mega\", name: \"Mega Menu\", description: \"Large dropdown navigation\" },\n  { id: \"hamburger\", name: \"Hamburger\", description: \"Mobile-first hamburger menu\" }\n]\n\nconst heroSections = [\n  { id: \"hero1\", name: \"Hero with Image\", description: \"Large hero with background image\" },\n  { id: \"hero2\", name: \"Split Hero\", description: \"Text on left, image on right\" },\n  { id: \"hero3\", name: \"Centered Hero\", description: \"Centered text with call-to-action\" },\n  { id: \"hero4\", name: \"Video Hero\", description: \"Hero with background video\" },\n  { id: \"hero5\", name: \"Carousel Hero\", description: \"Multiple slides with transitions\" },\n  { id: \"hero6\", name: \"Parallax Hero\", description: \"Parallax scrolling effect\" },\n  { id: \"hero7\", name: \"Animated Hero\", description: \"Text animations and effects\" },\n  { id: \"hero8\", name: \"Form Hero\", description: \"Hero with integrated form\" }\n]\n\nconst footerStyles = [\n  { id: \"simple\", name: \"Simple\", description: \"Minimal footer with links\" },\n  { id: \"detailed\", name: \"Detailed\", description: \"Multi-column footer\" },\n  { id: \"newsletter\", name: \"Newsletter\", description: \"Footer with newsletter signup\" },\n  { id: \"social\", name: \"Social\", description: \"Footer focused on social links\" },\n  { id: \"contact\", name: \"Contact\", description: \"Footer with contact information\" },\n  { id: \"sitemap\", name: \"Sitemap\", description: \"Comprehensive site navigation\" },\n  { id: \"minimal\", name: \"Minimal\", description: \"Ultra-clean minimal footer\" },\n  { id: \"corporate\", name: \"Corporate\", description: \"Professional business footer\" }\n]\n\nconst layoutStyles = [\n  { id: \"boxed\", name: \"Boxed\", description: \"Contained layout with margins\" },\n  { id: \"fullwidth\", name: \"Full Width\", description: \"Edge-to-edge layout\" },\n  { id: \"fluid\", name: \"Fluid\", description: \"Responsive fluid layout\" },\n  { id: \"grid\", name: \"Grid\", description: \"CSS Grid based layout\" },\n  { id: \"flexbox\", name: \"Flexbox\", description: \"Flexible box layout\" }\n]\n\nconst colorSchemes = [\n  { id: \"blue\", name: \"Ocean Blue\", primary: \"#3B82F6\", secondary: \"#1E40AF\", accent: \"#60A5FA\" },\n  { id: \"purple\", name: \"Royal Purple\", primary: \"#8B5CF6\", secondary: \"#7C3AED\", accent: \"#A78BFA\" },\n  { id: \"green\", name: \"Nature Green\", primary: \"#10B981\", secondary: \"#059669\", accent: \"#34D399\" },\n  { id: \"red\", name: \"Passion Red\", primary: \"#EF4444\", secondary: \"#DC2626\", accent: \"#F87171\" },\n  { id: \"orange\", name: \"Sunset Orange\", primary: \"#F97316\", secondary: \"#EA580C\", accent: \"#FB923C\" },\n  { id: \"pink\", name: \"Rose Pink\", primary: \"#EC4899\", secondary: \"#DB2777\", accent: \"#F472B6\" },\n  { id: \"indigo\", name: \"Deep Indigo\", primary: \"#6366F1\", secondary: \"#4F46E5\", accent: \"#818CF8\" },\n  { id: \"teal\", name: \"Ocean Teal\", primary: \"#14B8A6\", secondary: \"#0D9488\", accent: \"#5EEAD4\" }\n]\n\nconst fontFamilies = [\n  { id: \"inter\", name: \"Inter\", description: \"Modern sans-serif\" },\n  { id: \"roboto\", name: \"Roboto\", description: \"Google's signature font\" },\n  { id: \"opensans\", name: \"Open Sans\", description: \"Friendly and readable\" },\n  { id: \"lato\", name: \"Lato\", description: \"Humanist sans-serif\" },\n  { id: \"montserrat\", name: \"Montserrat\", description: \"Geometric sans-serif\" },\n  { id: \"playfair\", name: \"Playfair Display\", description: \"Elegant serif\" },\n  { id: \"merriweather\", name: \"Merriweather\", description: \"Reading-focused serif\" },\n  { id: \"poppins\", name: \"Poppins\", description: \"Rounded geometric\" }\n]\n\nconst animationTypes = [\n  { id: \"none\", name: \"None\", description: \"No animations\" },\n  { id: \"fade\", name: \"Fade In\", description: \"Smooth fade transitions\" },\n  { id: \"slide\", name: \"Slide Up\", description: \"Elements slide into view\" },\n  { id: \"zoom\", name: \"Zoom In\", description: \"Scale animations\" },\n  { id: \"bounce\", name: \"Bounce\", description: \"Playful bounce effects\" },\n  { id: \"rotate\", name: \"Rotate\", description: \"Rotation animations\" },\n  { id: \"flip\", name: \"Flip\", description: \"3D flip effects\" },\n  { id: \"typewriter\", name: \"Typewriter\", description: \"Text typing effect\" }\n]\n\nexport default function CustomizePage() {\n  // Layout & Structure\n  const [navbarStyle, setNavbarStyle] = useState(\"modern\")\n  const [heroSection, setHeroSection] = useState(\"hero1\")\n  const [footerStyle, setFooterStyle] = useState(\"simple\")\n  const [layoutStyle, setLayoutStyle] = useState(\"fullwidth\")\n\n  // Colors & Theme\n  const [colorScheme, setColorScheme] = useState(\"blue\")\n  const [primaryColor, setPrimaryColor] = useState(\"#3B82F6\")\n  const [secondaryColor, setSecondaryColor] = useState(\"#1E40AF\")\n  const [accentColor, setAccentColor] = useState(\"#60A5FA\")\n  const [backgroundColor, setBackgroundColor] = useState(\"#FFFFFF\")\n  const [textColor, setTextColor] = useState(\"#1F2937\")\n  const [darkMode, setDarkMode] = useState(false)\n\n  // Typography\n  const [fontFamily, setFontFamily] = useState(\"inter\")\n  const [fontSize, setFontSize] = useState([16])\n  const [lineHeight, setLineHeight] = useState([1.6])\n  const [fontWeight, setFontWeight] = useState(\"400\")\n\n  // Content\n  const [siteName, setSiteName] = useState(\"Your Brand\")\n  const [tagline, setTagline] = useState(\"Create amazing experiences\")\n  const [description, setDescription] = useState(\"Professional templates for modern websites\")\n  const [logoUrl, setLogoUrl] = useState(\"\")\n\n  // Contact Information\n  const [email, setEmail] = useState(\"\")\n  const [phone, setPhone] = useState(\"\")\n  const [address, setAddress] = useState(\"\")\n\n  // Social Media\n  const [facebook, setFacebook] = useState(\"\")\n  const [twitter, setTwitter] = useState(\"\")\n  const [instagram, setInstagram] = useState(\"\")\n  const [linkedin, setLinkedin] = useState(\"\")\n  const [youtube, setYoutube] = useState(\"\")\n\n  // Features\n  const [animations, setAnimations] = useState(\"fade\")\n  const [rtl, setRtl] = useState(false)\n  const [multiLanguage, setMultiLanguage] = useState(false)\n\n  // SEO\n  const [metaTitle, setMetaTitle] = useState(\"\")\n  const [metaDescription, setMetaDescription] = useState(\"\")\n  const [keywords, setKeywords] = useState(\"\")\n\n  // Advanced\n  const [customCSS, setCustomCSS] = useState(\"\")\n  const [customJS, setCustomJS] = useState(\"\")\n  const [googleAnalytics, setGoogleAnalytics] = useState(\"\")\n\n  // Responsive\n  const [mobileLayout, setMobileLayout] = useState(\"mobile\")\n  const [tabletLayout, setTabletLayout] = useState(\"tablet\")\n  const [desktopLayout, setDesktopLayout] = useState(\"desktop\")\n\n  // Component state\n  const [user, setUser] = useState<any>(null)\n  const [saving, setSaving] = useState(false)\n  const [activeTab, setActiveTab] = useState(\"layout\")\n  const [previewDevice, setPreviewDevice] = useState(\"desktop\")\n\n  const supabase = createClient()\n\n  useEffect(() => {\n    checkUser()\n  }, [])\n\n  const checkUser = async () => {\n    const { data: { user } } = await supabase.auth.getUser()\n    setUser(user)\n  }\n\n  const saveCustomization = async () => {\n    if (!user) {\n      toast.error('Please login to save customizations')\n      return\n    }\n\n    setSaving(true)\n    try {\n      const config = {\n        // Layout & Structure\n        navbarStyle,\n        heroSection,\n        footerStyle,\n        layoutStyle,\n\n        // Colors & Theme\n        colorScheme,\n        primaryColor,\n        secondaryColor,\n        accentColor,\n        backgroundColor,\n        textColor,\n        darkMode,\n\n        // Typography\n        fontFamily,\n        fontSize: fontSize[0],\n        lineHeight: lineHeight[0],\n        fontWeight,\n\n        // Content\n        siteName,\n        tagline,\n        description,\n        logoUrl,\n\n        // Contact Information\n        email,\n        phone,\n        address,\n\n        // Social Media\n        facebook,\n        twitter,\n        instagram,\n        linkedin,\n        youtube,\n\n        // Features\n        animations,\n        rtl,\n        multiLanguage,\n\n        // SEO\n        metaTitle,\n        metaDescription,\n        keywords,\n\n        // Advanced\n        customCSS,\n        customJS,\n        googleAnalytics,\n\n        // Responsive\n        mobileLayout,\n        tabletLayout,\n        desktopLayout,\n\n        timestamp: new Date().toISOString()\n      }\n\n      const { error } = await supabase\n        .from('customizations')\n        .insert({\n          user_id: user.id,\n          navbar_style: navbarStyle,\n          hero_section: heroSection,\n          footer_style: footerStyle,\n          config\n        })\n\n      if (error) throw error\n\n      toast.success('Customization saved successfully!')\n    } catch (error) {\n      console.error('Error saving customization:', error)\n      toast.error('Failed to save customization')\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const contactToBuy = async () => {\n    if (!user) {\n      toast.error('Please login to contact us')\n      return\n    }\n\n    try {\n      const config = {\n        navbarStyle,\n        heroSection,\n        footerStyle,\n        timestamp: new Date().toISOString()\n      }\n\n      // Save the customization first\n      const { error: saveError } = await supabase\n        .from('customizations')\n        .insert({\n          user_id: user.id,\n          navbar_style: navbarStyle,\n          hero_section: heroSection,\n          footer_style: footerStyle,\n          config\n        })\n\n      if (saveError) throw saveError\n\n      // Create a contact request\n      const { error: contactError } = await supabase\n        .from('contact_requests')\n        .insert({\n          name: user.user_metadata?.full_name || user.email,\n          email: user.email,\n          message: `I'm interested in purchasing a custom template with the following configuration:\n\nNavbar Style: ${navbarStyles.find(n => n.id === navbarStyle)?.name}\nHero Section: ${heroSections.find(h => h.id === heroSection)?.name}\nFooter Style: ${footerStyles.find(f => f.id === footerStyle)?.name}\n\nPlease contact me with pricing and timeline information.`\n        })\n\n      if (contactError) throw contactError\n\n      toast.success('Your customization has been saved and we will contact you soon!')\n    } catch (error) {\n      console.error('Error:', error)\n      toast.error('Failed to process request')\n    }\n  }\n\n  const getNavbarStyles = (style: string) => {\n    switch (style) {\n      case \"modern\":\n        return \"bg-white shadow-sm\"\n      case \"classic\":\n        return \"bg-gray-100\"\n      case \"sidebar\":\n        return \"bg-gray-900 text-white\"\n      case \"floating\":\n        return \"bg-white shadow-lg rounded-lg mx-4 mt-4\"\n      default:\n        return \"bg-white\"\n    }\n  }\n\n  const getHeroStyles = (style: string) => {\n    switch (style) {\n      case \"hero1\":\n        return \"bg-gradient-to-r from-blue-500 to-purple-600 text-white text-center\"\n      case \"hero2\":\n        return \"bg-gray-50\"\n      case \"hero3\":\n        return \"bg-white text-center\"\n      case \"hero4\":\n        return \"bg-black text-white text-center\"\n      default:\n        return \"bg-gray-50\"\n    }\n  }\n\n  const getFooterStyles = (style: string) => {\n    switch (style) {\n      case \"simple\":\n        return \"bg-gray-100 text-center\"\n      case \"detailed\":\n        return \"bg-gray-900 text-white\"\n      case \"newsletter\":\n        return \"bg-blue-50\"\n      case \"social\":\n        return \"bg-gray-800 text-white text-center\"\n      default:\n        return \"bg-gray-100\"\n    }\n  }\n\n  const renderHeroContent = (style: string) => {\n    switch (style) {\n      case \"hero1\":\n        return (\n          <div>\n            <h1 className=\"text-4xl font-bold mb-4\">Welcome to Your Website</h1>\n            <p className=\"text-xl mb-6\">Create amazing experiences with our templates</p>\n            <Button className=\"bg-white text-blue-600 hover:bg-gray-100\">Get Started</Button>\n          </div>\n        )\n      case \"hero2\":\n        return (\n          <div className=\"grid md:grid-cols-2 gap-8 items-center\">\n            <div>\n              <h1 className=\"text-4xl font-bold mb-4\">Split Hero Section</h1>\n              <p className=\"text-lg mb-6\">Text content on the left side with image on the right</p>\n              <Button>Learn More</Button>\n            </div>\n            <div className=\"bg-gray-200 h-64 rounded-lg flex items-center justify-center\">\n              <span className=\"text-gray-500\">Hero Image</span>\n            </div>\n          </div>\n        )\n      case \"hero3\":\n        return (\n          <div>\n            <h1 className=\"text-4xl font-bold mb-4\">Centered Hero</h1>\n            <p className=\"text-lg mb-6\">Perfect for landing pages and focused messaging</p>\n            <div className=\"flex gap-4 justify-center\">\n              <Button>Primary Action</Button>\n              <Button variant=\"outline\">Secondary Action</Button>\n            </div>\n          </div>\n        )\n      case \"hero4\":\n        return (\n          <div>\n            <h1 className=\"text-4xl font-bold mb-4\">Video Hero Background</h1>\n            <p className=\"text-xl mb-6\">Engaging video background for maximum impact</p>\n            <Button className=\"bg-white text-black hover:bg-gray-100\">Watch Demo</Button>\n          </div>\n        )\n      default:\n        return <div>Hero Content</div>\n    }\n  }\n\n  const renderFooterContent = (style: string) => {\n    switch (style) {\n      case \"simple\":\n        return (\n          <div>\n            <p>&copy; 2024 Your Company. All rights reserved.</p>\n          </div>\n        )\n      case \"detailed\":\n        return (\n          <div className=\"grid md:grid-cols-4 gap-6\">\n            <div>\n              <h4 className=\"font-semibold mb-2\">Company</h4>\n              <ul className=\"space-y-1 text-sm\">\n                <li>About</li>\n                <li>Careers</li>\n                <li>Contact</li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-semibold mb-2\">Products</h4>\n              <ul className=\"space-y-1 text-sm\">\n                <li>Templates</li>\n                <li>Tools</li>\n                <li>Support</li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-semibold mb-2\">Resources</h4>\n              <ul className=\"space-y-1 text-sm\">\n                <li>Blog</li>\n                <li>Documentation</li>\n                <li>Help Center</li>\n              </ul>\n            </div>\n            <div>\n              <h4 className=\"font-semibold mb-2\">Legal</h4>\n              <ul className=\"space-y-1 text-sm\">\n                <li>Privacy</li>\n                <li>Terms</li>\n                <li>Cookies</li>\n              </ul>\n            </div>\n          </div>\n        )\n      case \"newsletter\":\n        return (\n          <div className=\"text-center\">\n            <h4 className=\"font-semibold mb-2\">Subscribe to our newsletter</h4>\n            <p className=\"text-sm mb-4\">Get the latest updates and offers</p>\n            <div className=\"flex gap-2 justify-center max-w-md mx-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-3 py-2 border rounded\"\n              />\n              <Button>Subscribe</Button>\n            </div>\n          </div>\n        )\n      case \"social\":\n        return (\n          <div>\n            <div className=\"flex justify-center gap-4 mb-4\">\n              <span>📘</span>\n              <span>🐦</span>\n              <span>📷</span>\n              <span>💼</span>\n            </div>\n            <p>&copy; 2024 Your Company. Follow us on social media.</p>\n          </div>\n        )\n      default:\n        return <div>Footer Content</div>\n    }\n  }\n\n  // Helper functions for color scheme changes\n  const applyColorScheme = (scheme: any) => {\n    setPrimaryColor(scheme.primary)\n    setSecondaryColor(scheme.secondary)\n    setAccentColor(scheme.accent)\n  }\n\n  const resetToDefaults = () => {\n    setNavbarStyle(\"modern\")\n    setHeroSection(\"hero1\")\n    setFooterStyle(\"simple\")\n    setLayoutStyle(\"fullwidth\")\n    setColorScheme(\"blue\")\n    setPrimaryColor(\"#3B82F6\")\n    setSecondaryColor(\"#1E40AF\")\n    setAccentColor(\"#60A5FA\")\n    setBackgroundColor(\"#FFFFFF\")\n    setTextColor(\"#1F2937\")\n    setDarkMode(false)\n    setFontFamily(\"inter\")\n    setFontSize([16])\n    setLineHeight([1.6])\n    setFontWeight(\"400\")\n    setSiteName(\"Your Brand\")\n    setTagline(\"Create amazing experiences\")\n    setDescription(\"Professional templates for modern websites\")\n    setAnimations(\"fade\")\n    setRtl(false)\n    setMultiLanguage(false)\n    toast.success('Reset to default settings')\n  }\n\n  const exportConfig = () => {\n    const config = {\n      navbarStyle, heroSection, footerStyle, layoutStyle,\n      colorScheme, primaryColor, secondaryColor, accentColor,\n      backgroundColor, textColor, darkMode, fontFamily,\n      fontSize: fontSize[0], lineHeight: lineHeight[0], fontWeight,\n      siteName, tagline, description, logoUrl, email, phone, address,\n      facebook, twitter, instagram, linkedin, youtube,\n      animations, rtl, multiLanguage, metaTitle, metaDescription, keywords,\n      customCSS, customJS, googleAnalytics,\n      mobileLayout, tabletLayout, desktopLayout\n    }\n\n    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })\n    const url = URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `template-config-${new Date().toISOString().split('T')[0]}.json`\n    a.click()\n    URL.revokeObjectURL(url)\n    toast.success('Configuration exported!')\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Advanced Template Customizer</h1>\n          <p className=\"text-muted-foreground\">\n            Create your perfect website with our comprehensive customization tools\n          </p>\n        </div>\n        <div className=\"flex gap-2\">\n          <Button variant=\"outline\" size=\"sm\" onClick={resetToDefaults}>\n            <RefreshCw className=\"h-4 w-4 mr-2\" />\n            Reset\n          </Button>\n          <Button variant=\"outline\" size=\"sm\" onClick={exportConfig}>\n            <Download className=\"h-4 w-4 mr-2\" />\n            Export\n          </Button>\n          <Button variant=\"outline\" size=\"sm\" onClick={saveCustomization} disabled={saving || !user}>\n            <Save className=\"h-4 w-4 mr-2\" />\n            {saving ? 'Saving...' : 'Save'}\n          </Button>\n          <Button size=\"sm\" onClick={contactToBuy} disabled={!user}>\n            <Mail className=\"h-4 w-4 mr-2\" />\n            Contact to Buy\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        {/* Enhanced Customization Panel */}\n        <div className=\"lg:col-span-1\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-lg\">Customization Studio</CardTitle>\n              <CardDescription>\n                Professional-grade customization tools\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n                <TabsList className=\"grid w-full grid-cols-2\">\n                  <TabsTrigger value=\"layout\" className=\"text-xs\">\n                    <Layout className=\"h-3 w-3 mr-1\" />\n                    Layout\n                  </TabsTrigger>\n                  <TabsTrigger value=\"design\" className=\"text-xs\">\n                    <Palette className=\"h-3 w-3 mr-1\" />\n                    Design\n                  </TabsTrigger>\n                </TabsList>\n\n                <div className=\"mt-4 space-y-4\">\n                  <TabsContent value=\"layout\" className=\"space-y-4 mt-0\">\n                    {/* Layout & Structure */}\n                    <div className=\"space-y-3\">\n                      <Label className=\"text-sm font-semibold\">Layout Structure</Label>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Layout Style</Label>\n                        <Select value={layoutStyle} onValueChange={setLayoutStyle}>\n                          <SelectTrigger className=\"h-8\">\n                            <SelectValue />\n                          </SelectTrigger>\n                          <SelectContent>\n                            {layoutStyles.map((layout) => (\n                              <SelectItem key={layout.id} value={layout.id}>\n                                <div>\n                                  <div className=\"font-medium text-xs\">{layout.name}</div>\n                                  <div className=\"text-xs text-muted-foreground\">{layout.description}</div>\n                                </div>\n                              </SelectItem>\n                            ))}\n                          </SelectContent>\n                        </Select>\n                      </div>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Navbar Style</Label>\n                        <Select value={navbarStyle} onValueChange={setNavbarStyle}>\n                          <SelectTrigger className=\"h-8\">\n                            <SelectValue />\n                          </SelectTrigger>\n                          <SelectContent>\n                            {navbarStyles.map((style) => (\n                              <SelectItem key={style.id} value={style.id}>\n                                <div>\n                                  <div className=\"font-medium text-xs\">{style.name}</div>\n                                  <div className=\"text-xs text-muted-foreground\">{style.description}</div>\n                                </div>\n                              </SelectItem>\n                            ))}\n                          </SelectContent>\n                        </Select>\n                      </div>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Hero Section</Label>\n                        <Select value={heroSection} onValueChange={setHeroSection}>\n                          <SelectTrigger className=\"h-8\">\n                            <SelectValue />\n                          </SelectTrigger>\n                          <SelectContent>\n                            {heroSections.map((hero) => (\n                              <SelectItem key={hero.id} value={hero.id}>\n                                <div>\n                                  <div className=\"font-medium text-xs\">{hero.name}</div>\n                                  <div className=\"text-xs text-muted-foreground\">{hero.description}</div>\n                                </div>\n                              </SelectItem>\n                            ))}\n                          </SelectContent>\n                        </Select>\n                      </div>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Footer Style</Label>\n                        <Select value={footerStyle} onValueChange={setFooterStyle}>\n                          <SelectTrigger className=\"h-8\">\n                            <SelectValue />\n                          </SelectTrigger>\n                          <SelectContent>\n                            {footerStyles.map((footer) => (\n                              <SelectItem key={footer.id} value={footer.id}>\n                                <div>\n                                  <div className=\"font-medium text-xs\">{footer.name}</div>\n                                  <div className=\"text-xs text-muted-foreground\">{footer.description}</div>\n                                </div>\n                              </SelectItem>\n                            ))}\n                          </SelectContent>\n                        </Select>\n                      </div>\n                    </div>\n\n                    {/* Content Settings */}\n                    <div className=\"space-y-3\">\n                      <Label className=\"text-sm font-semibold\">Content</Label>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Site Name</Label>\n                        <Input\n                          value={siteName}\n                          onChange={(e) => setSiteName(e.target.value)}\n                          placeholder=\"Your Brand\"\n                          className=\"h-8\"\n                        />\n                      </div>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Tagline</Label>\n                        <Input\n                          value={tagline}\n                          onChange={(e) => setTagline(e.target.value)}\n                          placeholder=\"Your tagline\"\n                          className=\"h-8\"\n                        />\n                      </div>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Description</Label>\n                        <Textarea\n                          value={description}\n                          onChange={(e) => setDescription(e.target.value)}\n                          placeholder=\"Site description\"\n                          className=\"h-16 text-xs\"\n                        />\n                      </div>\n                    </div>\n\n                    {/* Responsive Settings */}\n                    <div className=\"space-y-3\">\n                      <Label className=\"text-sm font-semibold\">Responsive Design</Label>\n\n                      <div className=\"grid grid-cols-3 gap-1\">\n                        <Button\n                          variant={previewDevice === \"mobile\" ? \"default\" : \"outline\"}\n                          size=\"sm\"\n                          onClick={() => setPreviewDevice(\"mobile\")}\n                          className=\"h-8 text-xs\"\n                        >\n                          <Smartphone className=\"h-3 w-3\" />\n                        </Button>\n                        <Button\n                          variant={previewDevice === \"tablet\" ? \"default\" : \"outline\"}\n                          size=\"sm\"\n                          onClick={() => setPreviewDevice(\"tablet\")}\n                          className=\"h-8 text-xs\"\n                        >\n                          <Tablet className=\"h-3 w-3\" />\n                        </Button>\n                        <Button\n                          variant={previewDevice === \"desktop\" ? \"default\" : \"outline\"}\n                          size=\"sm\"\n                          onClick={() => setPreviewDevice(\"desktop\")}\n                          className=\"h-8 text-xs\"\n                        >\n                          <Monitor className=\"h-3 w-3\" />\n                        </Button>\n                      </div>\n                    </div>\n                  </TabsContent>\n\n                  <TabsContent value=\"design\" className=\"space-y-4 mt-0\">\n                    {/* Color Schemes */}\n                    <div className=\"space-y-3\">\n                      <Label className=\"text-sm font-semibold\">Color Scheme</Label>\n\n                      <div className=\"grid grid-cols-2 gap-2\">\n                        {colorSchemes.map((scheme) => (\n                          <Button\n                            key={scheme.id}\n                            variant={colorScheme === scheme.id ? \"default\" : \"outline\"}\n                            size=\"sm\"\n                            onClick={() => {\n                              setColorScheme(scheme.id)\n                              applyColorScheme(scheme)\n                            }}\n                            className=\"h-8 text-xs justify-start\"\n                          >\n                            <div\n                              className=\"w-3 h-3 rounded-full mr-2\"\n                              style={{ backgroundColor: scheme.primary }}\n                            />\n                            {scheme.name}\n                          </Button>\n                        ))}\n                      </div>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Primary Color</Label>\n                        <div className=\"flex gap-2\">\n                          <Input\n                            type=\"color\"\n                            value={primaryColor}\n                            onChange={(e) => setPrimaryColor(e.target.value)}\n                            className=\"w-12 h-8 p-1\"\n                          />\n                          <Input\n                            value={primaryColor}\n                            onChange={(e) => setPrimaryColor(e.target.value)}\n                            className=\"h-8 text-xs\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Secondary Color</Label>\n                        <div className=\"flex gap-2\">\n                          <Input\n                            type=\"color\"\n                            value={secondaryColor}\n                            onChange={(e) => setSecondaryColor(e.target.value)}\n                            className=\"w-12 h-8 p-1\"\n                          />\n                          <Input\n                            value={secondaryColor}\n                            onChange={(e) => setSecondaryColor(e.target.value)}\n                            className=\"h-8 text-xs\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center justify-between\">\n                        <Label className=\"text-xs\">Dark Mode</Label>\n                        <Button\n                          variant={darkMode ? \"default\" : \"outline\"}\n                          size=\"sm\"\n                          onClick={() => setDarkMode(!darkMode)}\n                          className=\"h-6 w-12\"\n                        >\n                          {darkMode ? <Moon className=\"h-3 w-3\" /> : <Sun className=\"h-3 w-3\" />}\n                        </Button>\n                      </div>\n                    </div>\n\n                    {/* Typography */}\n                    <div className=\"space-y-3\">\n                      <Label className=\"text-sm font-semibold\">Typography</Label>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Font Family</Label>\n                        <Select value={fontFamily} onValueChange={setFontFamily}>\n                          <SelectTrigger className=\"h-8\">\n                            <SelectValue />\n                          </SelectTrigger>\n                          <SelectContent>\n                            {fontFamilies.map((font) => (\n                              <SelectItem key={font.id} value={font.id}>\n                                <div>\n                                  <div className=\"font-medium text-xs\">{font.name}</div>\n                                  <div className=\"text-xs text-muted-foreground\">{font.description}</div>\n                                </div>\n                              </SelectItem>\n                            ))}\n                          </SelectContent>\n                        </Select>\n                      </div>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Font Size: {fontSize[0]}px</Label>\n                        <Slider\n                          value={fontSize}\n                          onValueChange={setFontSize}\n                          max={24}\n                          min={12}\n                          step={1}\n                          className=\"w-full\"\n                        />\n                      </div>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Line Height: {lineHeight[0]}</Label>\n                        <Slider\n                          value={lineHeight}\n                          onValueChange={setLineHeight}\n                          max={2.5}\n                          min={1}\n                          step={0.1}\n                          className=\"w-full\"\n                        />\n                      </div>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Font Weight</Label>\n                        <Select value={fontWeight} onValueChange={setFontWeight}>\n                          <SelectTrigger className=\"h-8\">\n                            <SelectValue />\n                          </SelectTrigger>\n                          <SelectContent>\n                            <SelectItem value=\"300\">Light (300)</SelectItem>\n                            <SelectItem value=\"400\">Regular (400)</SelectItem>\n                            <SelectItem value=\"500\">Medium (500)</SelectItem>\n                            <SelectItem value=\"600\">Semibold (600)</SelectItem>\n                            <SelectItem value=\"700\">Bold (700)</SelectItem>\n                          </SelectContent>\n                        </Select>\n                      </div>\n                    </div>\n\n                    {/* Animations & Effects */}\n                    <div className=\"space-y-3\">\n                      <Label className=\"text-sm font-semibold\">Animations</Label>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Animation Type</Label>\n                        <Select value={animations} onValueChange={setAnimations}>\n                          <SelectTrigger className=\"h-8\">\n                            <SelectValue />\n                          </SelectTrigger>\n                          <SelectContent>\n                            {animationTypes.map((animation) => (\n                              <SelectItem key={animation.id} value={animation.id}>\n                                <div>\n                                  <div className=\"font-medium text-xs\">{animation.name}</div>\n                                  <div className=\"text-xs text-muted-foreground\">{animation.description}</div>\n                                </div>\n                              </SelectItem>\n                            ))}\n                          </SelectContent>\n                        </Select>\n                      </div>\n                    </div>\n\n                    {/* Social Media */}\n                    <div className=\"space-y-3\">\n                      <Label className=\"text-sm font-semibold\">Social Media</Label>\n\n                      <div className=\"space-y-2\">\n                        <div className=\"flex items-center gap-2\">\n                          <Facebook className=\"h-3 w-3\" />\n                          <Input\n                            value={facebook}\n                            onChange={(e) => setFacebook(e.target.value)}\n                            placeholder=\"Facebook URL\"\n                            className=\"h-8 text-xs\"\n                          />\n                        </div>\n\n                        <div className=\"flex items-center gap-2\">\n                          <Twitter className=\"h-3 w-3\" />\n                          <Input\n                            value={twitter}\n                            onChange={(e) => setTwitter(e.target.value)}\n                            placeholder=\"Twitter URL\"\n                            className=\"h-8 text-xs\"\n                          />\n                        </div>\n\n                        <div className=\"flex items-center gap-2\">\n                          <Instagram className=\"h-3 w-3\" />\n                          <Input\n                            value={instagram}\n                            onChange={(e) => setInstagram(e.target.value)}\n                            placeholder=\"Instagram URL\"\n                            className=\"h-8 text-xs\"\n                          />\n                        </div>\n\n                        <div className=\"flex items-center gap-2\">\n                          <Linkedin className=\"h-3 w-3\" />\n                          <Input\n                            value={linkedin}\n                            onChange={(e) => setLinkedin(e.target.value)}\n                            placeholder=\"LinkedIn URL\"\n                            className=\"h-8 text-xs\"\n                          />\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Contact Information */}\n                    <div className=\"space-y-3\">\n                      <Label className=\"text-sm font-semibold\">Contact Info</Label>\n\n                      <div className=\"space-y-2\">\n                        <div className=\"flex items-center gap-2\">\n                          <Mail className=\"h-3 w-3\" />\n                          <Input\n                            value={email}\n                            onChange={(e) => setEmail(e.target.value)}\n                            placeholder=\"Email address\"\n                            className=\"h-8 text-xs\"\n                          />\n                        </div>\n\n                        <div className=\"flex items-center gap-2\">\n                          <Phone className=\"h-3 w-3\" />\n                          <Input\n                            value={phone}\n                            onChange={(e) => setPhone(e.target.value)}\n                            placeholder=\"Phone number\"\n                            className=\"h-8 text-xs\"\n                          />\n                        </div>\n\n                        <div className=\"flex items-center gap-2\">\n                          <MapPin className=\"h-3 w-3\" />\n                          <Input\n                            value={address}\n                            onChange={(e) => setAddress(e.target.value)}\n                            placeholder=\"Address\"\n                            className=\"h-8 text-xs\"\n                          />\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Advanced Features */}\n                    <div className=\"space-y-3\">\n                      <Label className=\"text-sm font-semibold\">Advanced</Label>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">SEO Title</Label>\n                        <Input\n                          value={metaTitle}\n                          onChange={(e) => setMetaTitle(e.target.value)}\n                          placeholder=\"SEO title\"\n                          className=\"h-8 text-xs\"\n                        />\n                      </div>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Meta Description</Label>\n                        <Textarea\n                          value={metaDescription}\n                          onChange={(e) => setMetaDescription(e.target.value)}\n                          placeholder=\"Meta description\"\n                          className=\"h-16 text-xs\"\n                        />\n                      </div>\n\n                      <div className=\"space-y-2\">\n                        <Label className=\"text-xs\">Custom CSS</Label>\n                        <Textarea\n                          value={customCSS}\n                          onChange={(e) => setCustomCSS(e.target.value)}\n                          placeholder=\"/* Custom CSS */\"\n                          className=\"h-20 text-xs font-mono\"\n                        />\n                      </div>\n                    </div>\n                  </TabsContent>\n                </div>\n              </Tabs>\n\n              {!user && (\n                <div className=\"mt-4 p-3 bg-muted rounded-lg\">\n                  <p className=\"text-xs text-muted-foreground\">\n                    Please login to save your customizations\n                  </p>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Live Preview */}\n        <div className=\"lg:col-span-3\">\n          <Card>\n            <CardHeader>\n              <div className=\"flex justify-between items-center\">\n                <div>\n                  <CardTitle className=\"text-lg flex items-center gap-2\">\n                    <Eye className=\"h-5 w-5\" />\n                    Live Preview\n                  </CardTitle>\n                  <CardDescription>\n                    See your changes in real-time across devices\n                  </CardDescription>\n                </div>\n                <div className=\"flex gap-2\">\n                  <Badge variant=\"outline\" className=\"text-xs\">\n                    {previewDevice === \"mobile\" && \"375px\"}\n                    {previewDevice === \"tablet\" && \"768px\"}\n                    {previewDevice === \"desktop\" && \"1200px\"}\n                  </Badge>\n                </div>\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div\n                className={`\n                  border rounded-lg bg-background min-h-[600px] mx-auto transition-all duration-300\n                  ${previewDevice === \"mobile\" ? \"max-w-[375px]\" : \"\"}\n                  ${previewDevice === \"tablet\" ? \"max-w-[768px]\" : \"\"}\n                  ${previewDevice === \"desktop\" ? \"max-w-full\" : \"\"}\n                  ${darkMode ? \"dark bg-gray-900 text-white\" : \"bg-white\"}\n                `}\n                style={{\n                  fontFamily: fontFamily === \"inter\" ? \"Inter, sans-serif\" :\n                             fontFamily === \"roboto\" ? \"Roboto, sans-serif\" :\n                             fontFamily === \"opensans\" ? \"Open Sans, sans-serif\" :\n                             fontFamily === \"lato\" ? \"Lato, sans-serif\" :\n                             fontFamily === \"montserrat\" ? \"Montserrat, sans-serif\" :\n                             fontFamily === \"playfair\" ? \"Playfair Display, serif\" :\n                             fontFamily === \"merriweather\" ? \"Merriweather, serif\" :\n                             fontFamily === \"poppins\" ? \"Poppins, sans-serif\" : \"Inter, sans-serif\",\n                  fontSize: `${fontSize[0]}px`,\n                  lineHeight: lineHeight[0],\n                  fontWeight: fontWeight,\n                  backgroundColor: darkMode ? \"#1F2937\" : backgroundColor,\n                  color: darkMode ? \"#F9FAFB\" : textColor,\n                  '--primary-color': primaryColor,\n                  '--secondary-color': secondaryColor,\n                  '--accent-color': accentColor\n                } as React.CSSProperties}\n              >\n                {/* Enhanced Navbar Preview */}\n                <div className=\"p-4 border-b\" style={{ borderColor: primaryColor }}>\n                  <div className=\"flex justify-between items-center\">\n                    <div className=\"font-bold text-lg\" style={{ color: primaryColor }}>\n                      {siteName}\n                    </div>\n                    <div className={`flex gap-4 ${previewDevice === \"mobile\" ? \"hidden\" : \"\"}`}>\n                      <span className=\"hover:opacity-80 cursor-pointer\">Home</span>\n                      <span className=\"hover:opacity-80 cursor-pointer\">About</span>\n                      <span className=\"hover:opacity-80 cursor-pointer\">Services</span>\n                      <span className=\"hover:opacity-80 cursor-pointer\">Contact</span>\n                    </div>\n                    {previewDevice === \"mobile\" && (\n                      <div className=\"w-6 h-6 flex flex-col justify-center\">\n                        <div className=\"w-full h-0.5 bg-current mb-1\"></div>\n                        <div className=\"w-full h-0.5 bg-current mb-1\"></div>\n                        <div className=\"w-full h-0.5 bg-current\"></div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Enhanced Hero Preview */}\n                <div className=\"p-6\" style={{ backgroundColor: `${primaryColor}10` }}>\n                  <div className=\"text-center\">\n                    <h1\n                      className=\"font-bold mb-4\"\n                      style={{\n                        color: primaryColor,\n                        fontSize: previewDevice === \"mobile\" ? \"2rem\" : previewDevice === \"tablet\" ? \"2.5rem\" : \"3rem\"\n                      }}\n                    >\n                      {siteName}\n                    </h1>\n                    <p\n                      className=\"mb-6\"\n                      style={{\n                        color: secondaryColor,\n                        fontSize: previewDevice === \"mobile\" ? \"1rem\" : \"1.25rem\"\n                      }}\n                    >\n                      {tagline}\n                    </p>\n                    <p className=\"mb-8 max-w-2xl mx-auto opacity-80\">\n                      {description}\n                    </p>\n                    <button\n                      className=\"px-6 py-3 rounded-lg font-semibold text-white transition-all hover:scale-105\"\n                      style={{\n                        backgroundColor: primaryColor,\n                        fontSize: previewDevice === \"mobile\" ? \"0.875rem\" : \"1rem\"\n                      }}\n                    >\n                      Get Started\n                    </button>\n                  </div>\n                </div>\n\n                {/* Features Section */}\n                <div className=\"p-6\">\n                  <h2\n                    className=\"text-2xl font-bold text-center mb-8\"\n                    style={{ color: primaryColor }}\n                  >\n                    Features\n                  </h2>\n                  <div className={`grid gap-6 ${previewDevice === \"mobile\" ? \"grid-cols-1\" : previewDevice === \"tablet\" ? \"grid-cols-2\" : \"grid-cols-3\"}`}>\n                    {[1, 2, 3].map((i) => (\n                      <div\n                        key={i}\n                        className=\"p-4 rounded-lg border text-center\"\n                        style={{\n                          borderColor: accentColor,\n                          backgroundColor: darkMode ? \"#374151\" : \"#F9FAFB\"\n                        }}\n                      >\n                        <div\n                          className=\"w-12 h-12 rounded-full mb-4 flex items-center justify-center mx-auto\"\n                          style={{ backgroundColor: accentColor }}\n                        >\n                          <Zap className=\"h-6 w-6 text-white\" />\n                        </div>\n                        <h3 className=\"font-semibold mb-2\">Feature {i}</h3>\n                        <p className=\"text-sm opacity-80\">\n                          Amazing feature description that showcases capabilities.\n                        </p>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Contact Section */}\n                {(email || phone || address) && (\n                  <div className=\"p-6\" style={{ backgroundColor: `${secondaryColor}10` }}>\n                    <h2\n                      className=\"text-2xl font-bold text-center mb-6\"\n                      style={{ color: secondaryColor }}\n                    >\n                      Contact Us\n                    </h2>\n                    <div className={`grid gap-4 ${previewDevice === \"mobile\" ? \"grid-cols-1\" : \"grid-cols-3\"}`}>\n                      {email && (\n                        <div className=\"flex items-center gap-3 justify-center\">\n                          <Mail className=\"h-5 w-5\" style={{ color: primaryColor }} />\n                          <span className=\"text-sm\">{email}</span>\n                        </div>\n                      )}\n                      {phone && (\n                        <div className=\"flex items-center gap-3 justify-center\">\n                          <Phone className=\"h-5 w-5\" style={{ color: primaryColor }} />\n                          <span className=\"text-sm\">{phone}</span>\n                        </div>\n                      )}\n                      {address && (\n                        <div className=\"flex items-center gap-3 justify-center\">\n                          <MapPin className=\"h-5 w-5\" style={{ color: primaryColor }} />\n                          <span className=\"text-sm\">{address}</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                )}\n\n                {/* Social Media Section */}\n                {(facebook || twitter || instagram || linkedin) && (\n                  <div className=\"p-6 text-center\">\n                    <h3 className=\"text-lg font-semibold mb-4\">Follow Us</h3>\n                    <div className=\"flex justify-center gap-4\">\n                      {facebook && (\n                        <div\n                          className=\"w-10 h-10 rounded-full flex items-center justify-center cursor-pointer hover:scale-110 transition-transform\"\n                          style={{ backgroundColor: primaryColor }}\n                        >\n                          <Facebook className=\"h-5 w-5 text-white\" />\n                        </div>\n                      )}\n                      {twitter && (\n                        <div\n                          className=\"w-10 h-10 rounded-full flex items-center justify-center cursor-pointer hover:scale-110 transition-transform\"\n                          style={{ backgroundColor: primaryColor }}\n                        >\n                          <Twitter className=\"h-5 w-5 text-white\" />\n                        </div>\n                      )}\n                      {instagram && (\n                        <div\n                          className=\"w-10 h-10 rounded-full flex items-center justify-center cursor-pointer hover:scale-110 transition-transform\"\n                          style={{ backgroundColor: primaryColor }}\n                        >\n                          <Instagram className=\"h-5 w-5 text-white\" />\n                        </div>\n                      )}\n                      {linkedin && (\n                        <div\n                          className=\"w-10 h-10 rounded-full flex items-center justify-center cursor-pointer hover:scale-110 transition-transform\"\n                          style={{ backgroundColor: primaryColor }}\n                        >\n                          <Linkedin className=\"h-5 w-5 text-white\" />\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                )}\n\n                {/* Enhanced Footer Preview */}\n                <div\n                  className=\"mt-8 p-6 border-t text-center\"\n                  style={{\n                    borderColor: accentColor,\n                    backgroundColor: darkMode ? \"#111827\" : \"#F3F4F6\"\n                  }}\n                >\n                  <p className=\"text-sm opacity-80\">\n                    © 2024 {siteName}. All rights reserved.\n                  </p>\n                  {(facebook || twitter || instagram || linkedin) && (\n                    <div className=\"flex justify-center gap-4 mt-4\">\n                      <span className=\"text-xs opacity-60\">Follow us:</span>\n                      {facebook && <span className=\"text-xs hover:opacity-80 cursor-pointer\">Facebook</span>}\n                      {twitter && <span className=\"text-xs hover:opacity-80 cursor-pointer\">Twitter</span>}\n                      {instagram && <span className=\"text-xs hover:opacity-80 cursor-pointer\">Instagram</span>}\n                      {linkedin && <span className=\"text-xs hover:opacity-80 cursor-pointer\">LinkedIn</span>}\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              {/* Configuration Summary */}\n              <div className=\"mt-6 p-4 bg-muted rounded-lg\">\n                <h4 className=\"font-semibold mb-2\">Current Configuration</h4>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                  <div>\n                    <span className=\"text-muted-foreground\">Layout:</span>\n                    <p className=\"font-medium\">{layoutStyle}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-muted-foreground\">Theme:</span>\n                    <p className=\"font-medium\">{colorScheme} {darkMode && \"(Dark)\"}</p>\n                  </div>\n                  <div>\n                    <span className=\"text-muted-foreground\">Font:</span>\n                    <p className=\"font-medium\">{fontFamily} {fontSize[0]}px</p>\n                  </div>\n                  <div>\n                    <span className=\"text-muted-foreground\">Animation:</span>\n                    <p className=\"font-medium\">{animations}</p>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n\n      {/* Export Options */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Export Options</CardTitle>\n          <CardDescription>\n            Download your customized template\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex gap-4\">\n            <Button>\n              <Download className=\"h-4 w-4 mr-2\" />\n              Download HTML/CSS\n            </Button>\n            <Button variant=\"outline\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Download React Components\n            </Button>\n            <Button variant=\"outline\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Export as PDF\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAfA;;;;;;;;;;;;;;AA+CA,iCAAiC;AACjC,MAAM,eAAe;IACnB;QAAE,IAAI;QAAU,MAAM;QAAU,aAAa;IAA+B;IAC5E;QAAE,IAAI;QAAW,MAAM;QAAW,aAAa;IAA8B;IAC7E;QAAE,IAAI;QAAW,MAAM;QAAW,aAAa;IAA2B;IAC1E;QAAE,IAAI;QAAY,MAAM;QAAY,aAAa;IAA0B;IAC3E;QAAE,IAAI;QAAU,MAAM;QAAU,aAAa;IAA6B;IAC1E;QAAE,IAAI;QAAe,MAAM;QAAe,aAAa;IAAiC;IACxF;QAAE,IAAI;QAAQ,MAAM;QAAa,aAAa;IAA4B;IAC1E;QAAE,IAAI;QAAa,MAAM;QAAa,aAAa;IAA8B;CAClF;AAED,MAAM,eAAe;IACnB;QAAE,IAAI;QAAS,MAAM;QAAmB,aAAa;IAAmC;IACxF;QAAE,IAAI;QAAS,MAAM;QAAc,aAAa;IAA+B;IAC/E;QAAE,IAAI;QAAS,MAAM;QAAiB,aAAa;IAAoC;IACvF;QAAE,IAAI;QAAS,MAAM;QAAc,aAAa;IAA6B;IAC7E;QAAE,IAAI;QAAS,MAAM;QAAiB,aAAa;IAAmC;IACtF;QAAE,IAAI;QAAS,MAAM;QAAiB,aAAa;IAA4B;IAC/E;QAAE,IAAI;QAAS,MAAM;QAAiB,aAAa;IAA8B;IACjF;QAAE,IAAI;QAAS,MAAM;QAAa,aAAa;IAA4B;CAC5E;AAED,MAAM,eAAe;IACnB;QAAE,IAAI;QAAU,MAAM;QAAU,aAAa;IAA4B;IACzE;QAAE,IAAI;QAAY,MAAM;QAAY,aAAa;IAAsB;IACvE;QAAE,IAAI;QAAc,MAAM;QAAc,aAAa;IAAgC;IACrF;QAAE,IAAI;QAAU,MAAM;QAAU,aAAa;IAAiC;IAC9E;QAAE,IAAI;QAAW,MAAM;QAAW,aAAa;IAAkC;IACjF;QAAE,IAAI;QAAW,MAAM;QAAW,aAAa;IAAgC;IAC/E;QAAE,IAAI;QAAW,MAAM;QAAW,aAAa;IAA6B;IAC5E;QAAE,IAAI;QAAa,MAAM;QAAa,aAAa;IAA+B;CACnF;AAED,MAAM,eAAe;IACnB;QAAE,IAAI;QAAS,MAAM;QAAS,aAAa;IAAgC;IAC3E;QAAE,IAAI;QAAa,MAAM;QAAc,aAAa;IAAsB;IAC1E;QAAE,IAAI;QAAS,MAAM;QAAS,aAAa;IAA0B;IACrE;QAAE,IAAI;QAAQ,MAAM;QAAQ,aAAa;IAAwB;IACjE;QAAE,IAAI;QAAW,MAAM;QAAW,aAAa;IAAsB;CACtE;AAED,MAAM,eAAe;IACnB;QAAE,IAAI;QAAQ,MAAM;QAAc,SAAS;QAAW,WAAW;QAAW,QAAQ;IAAU;IAC9F;QAAE,IAAI;QAAU,MAAM;QAAgB,SAAS;QAAW,WAAW;QAAW,QAAQ;IAAU;IAClG;QAAE,IAAI;QAAS,MAAM;QAAgB,SAAS;QAAW,WAAW;QAAW,QAAQ;IAAU;IACjG;QAAE,IAAI;QAAO,MAAM;QAAe,SAAS;QAAW,WAAW;QAAW,QAAQ;IAAU;IAC9F;QAAE,IAAI;QAAU,MAAM;QAAiB,SAAS;QAAW,WAAW;QAAW,QAAQ;IAAU;IACnG;QAAE,IAAI;QAAQ,MAAM;QAAa,SAAS;QAAW,WAAW;QAAW,QAAQ;IAAU;IAC7F;QAAE,IAAI;QAAU,MAAM;QAAe,SAAS;QAAW,WAAW;QAAW,QAAQ;IAAU;IACjG;QAAE,IAAI;QAAQ,MAAM;QAAc,SAAS;QAAW,WAAW;QAAW,QAAQ;IAAU;CAC/F;AAED,MAAM,eAAe;IACnB;QAAE,IAAI;QAAS,MAAM;QAAS,aAAa;IAAoB;IAC/D;QAAE,IAAI;QAAU,MAAM;QAAU,aAAa;IAA0B;IACvE;QAAE,IAAI;QAAY,MAAM;QAAa,aAAa;IAAwB;IAC1E;QAAE,IAAI;QAAQ,MAAM;QAAQ,aAAa;IAAsB;IAC/D;QAAE,IAAI;QAAc,MAAM;QAAc,aAAa;IAAuB;IAC5E;QAAE,IAAI;QAAY,MAAM;QAAoB,aAAa;IAAgB;IACzE;QAAE,IAAI;QAAgB,MAAM;QAAgB,aAAa;IAAwB;IACjF;QAAE,IAAI;QAAW,MAAM;QAAW,aAAa;IAAoB;CACpE;AAED,MAAM,iBAAiB;IACrB;QAAE,IAAI;QAAQ,MAAM;QAAQ,aAAa;IAAgB;IACzD;QAAE,IAAI;QAAQ,MAAM;QAAW,aAAa;IAA0B;IACtE;QAAE,IAAI;QAAS,MAAM;QAAY,aAAa;IAA2B;IACzE;QAAE,IAAI;QAAQ,MAAM;QAAW,aAAa;IAAmB;IAC/D;QAAE,IAAI;QAAU,MAAM;QAAU,aAAa;IAAyB;IACtE;QAAE,IAAI;QAAU,MAAM;QAAU,aAAa;IAAsB;IACnE;QAAE,IAAI;QAAQ,MAAM;QAAQ,aAAa;IAAkB;IAC3D;QAAE,IAAI;QAAc,MAAM;QAAc,aAAa;IAAqB;CAC3E;AAEc,SAAS;;IACtB,qBAAqB;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,iBAAiB;IACjB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,aAAa;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;KAAG;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;KAAI;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,UAAU;IACV,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,sBAAsB;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,eAAe;IACf,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,WAAW;IACX,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM;IACN,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,WAAW;IACX,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,aAAa;IACb,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,kBAAkB;IAClB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,YAAY;QAChB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACtD,QAAQ;IACV;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM;YACT,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,UAAU;QACV,IAAI;YACF,MAAM,SAAS;gBACb,qBAAqB;gBACrB;gBACA;gBACA;gBACA;gBAEA,iBAAiB;gBACjB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAEA,aAAa;gBACb;gBACA,UAAU,QAAQ,CAAC,EAAE;gBACrB,YAAY,UAAU,CAAC,EAAE;gBACzB;gBAEA,UAAU;gBACV;gBACA;gBACA;gBACA;gBAEA,sBAAsB;gBACtB;gBACA;gBACA;gBAEA,eAAe;gBACf;gBACA;gBACA;gBACA;gBACA;gBAEA,WAAW;gBACX;gBACA;gBACA;gBAEA,MAAM;gBACN;gBACA;gBACA;gBAEA,WAAW;gBACX;gBACA;gBACA;gBAEA,aAAa;gBACb;gBACA;gBACA;gBAEA,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,kBACL,MAAM,CAAC;gBACN,SAAS,KAAK,EAAE;gBAChB,cAAc;gBACd,cAAc;gBACd,cAAc;gBACd;YACF;YAEF,IAAI,OAAO,MAAM;YAEjB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,MAAM;YACT,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,SAAS;gBACb;gBACA;gBACA;gBACA,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,+BAA+B;YAC/B,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChC,IAAI,CAAC,kBACL,MAAM,CAAC;gBACN,SAAS,KAAK,EAAE;gBAChB,cAAc;gBACd,cAAc;gBACd,cAAc;gBACd;YACF;YAEF,IAAI,WAAW,MAAM;YAErB,2BAA2B;YAC3B,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,oBACL,MAAM,CAAC;gBACN,MAAM,KAAK,aAAa,EAAE,aAAa,KAAK,KAAK;gBACjD,OAAO,KAAK,KAAK;gBACjB,SAAS,CAAC;;cAEN,EAAE,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,KAAK;cACrD,EAAE,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,KAAK;cACrD,EAAE,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,cAAc,KAAK;;wDAEX,CAAC;YACjD;YAEF,IAAI,cAAc,MAAM;YAExB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,UAAU;YACxB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,6LAAC;4BAAE,WAAU;sCAAe;;;;;;sCAC5B,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;sCAA2C;;;;;;;;;;;;YAGnE,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA0B;;;;;;8CACxC,6LAAC;oCAAE,WAAU;8CAAe;;;;;;8CAC5B,6LAAC,qIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;;sCAEV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;;;;;;;YAIxC,KAAK;gBACH,qBACE,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,6LAAC;4BAAE,WAAU;sCAAe;;;;;;sCAC5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;8CAAC;;;;;;8CACR,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;8CAAU;;;;;;;;;;;;;;;;;;YAIlC,KAAK;gBACH,qBACE,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,6LAAC;4BAAE,WAAU;sCAAe;;;;;;sCAC5B,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;sCAAwC;;;;;;;;;;;;YAGhE;gBACE,qBAAO,6LAAC;8BAAI;;;;;;QAChB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,qBACE,6LAAC;8BACC,cAAA,6LAAC;kCAAE;;;;;;;;;;;YAGT,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAGR,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAGR,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAGR,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;YAKd,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,6LAAC;4BAAE,WAAU;sCAAe;;;;;;sCAC5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,6LAAC,qIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;;;;;;;;YAIhB,KAAK;gBACH,qBACE,6LAAC;;sCACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAK;;;;;;8CACN,6LAAC;8CAAK;;;;;;8CACN,6LAAC;8CAAK;;;;;;8CACN,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC;sCAAE;;;;;;;;;;;;YAGT;gBACE,qBAAO,6LAAC;8BAAI;;;;;;QAChB;IACF;IAEA,4CAA4C;IAC5C,MAAM,mBAAmB,CAAC;QACxB,gBAAgB,OAAO,OAAO;QAC9B,kBAAkB,OAAO,SAAS;QAClC,eAAe,OAAO,MAAM;IAC9B;IAEA,MAAM,kBAAkB;QACtB,eAAe;QACf,eAAe;QACf,eAAe;QACf,eAAe;QACf,eAAe;QACf,gBAAgB;QAChB,kBAAkB;QAClB,eAAe;QACf,mBAAmB;QACnB,aAAa;QACb,YAAY;QACZ,cAAc;QACd,YAAY;YAAC;SAAG;QAChB,cAAc;YAAC;SAAI;QACnB,cAAc;QACd,YAAY;QACZ,WAAW;QACX,eAAe;QACf,cAAc;QACd,OAAO;QACP,iBAAiB;QACjB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,eAAe;QACnB,MAAM,SAAS;YACb;YAAa;YAAa;YAAa;YACvC;YAAa;YAAc;YAAgB;YAC3C;YAAiB;YAAW;YAAU;YACtC,UAAU,QAAQ,CAAC,EAAE;YAAE,YAAY,UAAU,CAAC,EAAE;YAAE;YAClD;YAAU;YAAS;YAAa;YAAS;YAAO;YAAO;YACvD;YAAU;YAAS;YAAW;YAAU;YACxC;YAAY;YAAK;YAAe;YAAW;YAAiB;YAC5D;YAAW;YAAU;YACrB;YAAc;YAAc;QAC9B;QAEA,MAAM,OAAO,IAAI,KAAK;YAAC,KAAK,SAAS,CAAC,QAAQ,MAAM;SAAG,EAAE;YAAE,MAAM;QAAmB;QACpF,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,gBAAgB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;QAC7E,EAAE,KAAK;QACP,IAAI,eAAe,CAAC;QACpB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;;kDAC3C,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;;kDAC3C,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;gCAAmB,UAAU,UAAU,CAAC;;kDACnF,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,SAAS,cAAc;;;;;;;0CAE1B,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAS;gCAAc,UAAU,CAAC;;kDAClD,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAMvC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;sDAC/B,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC,mIAAA,CAAA,OAAI;4CAAC,OAAO;4CAAW,eAAe;4CAAc,WAAU;;8DAC7D,6LAAC,mIAAA,CAAA,WAAQ;oDAAC,WAAU;;sEAClB,6LAAC,mIAAA,CAAA,cAAW;4DAAC,OAAM;4DAAS,WAAU;;8EACpC,6LAAC,wNAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGrC,6LAAC,mIAAA,CAAA,cAAW;4DAAC,OAAM;4DAAS,WAAU;;8EACpC,6LAAC,2MAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;8DAKxC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mIAAA,CAAA,cAAW;4DAAC,OAAM;4DAAS,WAAU;;8EAEpC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,WAAU;sFAAwB;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC,qIAAA,CAAA,SAAM;oFAAC,OAAO;oFAAa,eAAe;;sGACzC,6LAAC,qIAAA,CAAA,gBAAa;4FAAC,WAAU;sGACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sGAEd,6LAAC,qIAAA,CAAA,gBAAa;sGACX,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC,qIAAA,CAAA,aAAU;oGAAiB,OAAO,OAAO,EAAE;8GAC1C,cAAA,6LAAC;;0HACC,6LAAC;gHAAI,WAAU;0HAAuB,OAAO,IAAI;;;;;;0HACjD,6LAAC;gHAAI,WAAU;0HAAiC,OAAO,WAAW;;;;;;;;;;;;mGAHrD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;sFAWlC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC,qIAAA,CAAA,SAAM;oFAAC,OAAO;oFAAa,eAAe;;sGACzC,6LAAC,qIAAA,CAAA,gBAAa;4FAAC,WAAU;sGACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sGAEd,6LAAC,qIAAA,CAAA,gBAAa;sGACX,aAAa,GAAG,CAAC,CAAC,sBACjB,6LAAC,qIAAA,CAAA,aAAU;oGAAgB,OAAO,MAAM,EAAE;8GACxC,cAAA,6LAAC;;0HACC,6LAAC;gHAAI,WAAU;0HAAuB,MAAM,IAAI;;;;;;0HAChD,6LAAC;gHAAI,WAAU;0HAAiC,MAAM,WAAW;;;;;;;;;;;;mGAHpD,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;sFAWjC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC,qIAAA,CAAA,SAAM;oFAAC,OAAO;oFAAa,eAAe;;sGACzC,6LAAC,qIAAA,CAAA,gBAAa;4FAAC,WAAU;sGACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sGAEd,6LAAC,qIAAA,CAAA,gBAAa;sGACX,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,qIAAA,CAAA,aAAU;oGAAe,OAAO,KAAK,EAAE;8GACtC,cAAA,6LAAC;;0HACC,6LAAC;gHAAI,WAAU;0HAAuB,KAAK,IAAI;;;;;;0HAC/C,6LAAC;gHAAI,WAAU;0HAAiC,KAAK,WAAW;;;;;;;;;;;;mGAHnD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;sFAWhC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC,qIAAA,CAAA,SAAM;oFAAC,OAAO;oFAAa,eAAe;;sGACzC,6LAAC,qIAAA,CAAA,gBAAa;4FAAC,WAAU;sGACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sGAEd,6LAAC,qIAAA,CAAA,gBAAa;sGACX,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC,qIAAA,CAAA,aAAU;oGAAiB,OAAO,OAAO,EAAE;8GAC1C,cAAA,6LAAC;;0HACC,6LAAC;gHAAI,WAAU;0HAAuB,OAAO,IAAI;;;;;;0HACjD,6LAAC;gHAAI,WAAU;0HAAiC,OAAO,WAAW;;;;;;;;;;;;mGAHrD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8EAapC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,WAAU;sFAAwB;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC,oIAAA,CAAA,QAAK;oFACJ,OAAO;oFACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oFAC3C,aAAY;oFACZ,WAAU;;;;;;;;;;;;sFAId,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC,oIAAA,CAAA,QAAK;oFACJ,OAAO;oFACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oFAC1C,aAAY;oFACZ,WAAU;;;;;;;;;;;;sFAId,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC,uIAAA,CAAA,WAAQ;oFACP,OAAO;oFACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oFAC9C,aAAY;oFACZ,WAAU;;;;;;;;;;;;;;;;;;8EAMhB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,WAAU;sFAAwB;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,qIAAA,CAAA,SAAM;oFACL,SAAS,kBAAkB,WAAW,YAAY;oFAClD,MAAK;oFACL,SAAS,IAAM,iBAAiB;oFAChC,WAAU;8FAEV,cAAA,6LAAC,iNAAA,CAAA,aAAU;wFAAC,WAAU;;;;;;;;;;;8FAExB,6LAAC,qIAAA,CAAA,SAAM;oFACL,SAAS,kBAAkB,WAAW,YAAY;oFAClD,MAAK;oFACL,SAAS,IAAM,iBAAiB;oFAChC,WAAU;8FAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;;;;;;8FAEpB,6LAAC,qIAAA,CAAA,SAAM;oFACL,SAAS,kBAAkB,YAAY,YAAY;oFACnD,MAAK;oFACL,SAAS,IAAM,iBAAiB;oFAChC,WAAU;8FAEV,cAAA,6LAAC,2MAAA,CAAA,UAAO;wFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAM3B,6LAAC,mIAAA,CAAA,cAAW;4DAAC,OAAM;4DAAS,WAAU;;8EAEpC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,WAAU;sFAAwB;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;sFACZ,aAAa,GAAG,CAAC,CAAC,uBACjB,6LAAC,qIAAA,CAAA,SAAM;oFAEL,SAAS,gBAAgB,OAAO,EAAE,GAAG,YAAY;oFACjD,MAAK;oFACL,SAAS;wFACP,eAAe,OAAO,EAAE;wFACxB,iBAAiB;oFACnB;oFACA,WAAU;;sGAEV,6LAAC;4FACC,WAAU;4FACV,OAAO;gGAAE,iBAAiB,OAAO,OAAO;4FAAC;;;;;;wFAE1C,OAAO,IAAI;;mFAbP,OAAO,EAAE;;;;;;;;;;sFAkBpB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,oIAAA,CAAA,QAAK;4FACJ,MAAK;4FACL,OAAO;4FACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4FAC/C,WAAU;;;;;;sGAEZ,6LAAC,oIAAA,CAAA,QAAK;4FACJ,OAAO;4FACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4FAC/C,WAAU;;;;;;;;;;;;;;;;;;sFAKhB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,oIAAA,CAAA,QAAK;4FACJ,MAAK;4FACL,OAAO;4FACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4FACjD,WAAU;;;;;;sGAEZ,6LAAC,oIAAA,CAAA,QAAK;4FACJ,OAAO;4FACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4FACjD,WAAU;;;;;;;;;;;;;;;;;;sFAKhB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC,qIAAA,CAAA,SAAM;oFACL,SAAS,WAAW,YAAY;oFAChC,MAAK;oFACL,SAAS,IAAM,YAAY,CAAC;oFAC5B,WAAU;8FAET,yBAAW,6LAAC,qMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;6GAAe,6LAAC,mMAAA,CAAA,MAAG;wFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8EAMhE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,WAAU;sFAAwB;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC,qIAAA,CAAA,SAAM;oFAAC,OAAO;oFAAY,eAAe;;sGACxC,6LAAC,qIAAA,CAAA,gBAAa;4FAAC,WAAU;sGACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sGAEd,6LAAC,qIAAA,CAAA,gBAAa;sGACX,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,qIAAA,CAAA,aAAU;oGAAe,OAAO,KAAK,EAAE;8GACtC,cAAA,6LAAC;;0HACC,6LAAC;gHAAI,WAAU;0HAAuB,KAAK,IAAI;;;;;;0HAC/C,6LAAC;gHAAI,WAAU;0HAAiC,KAAK,WAAW;;;;;;;;;;;;mGAHnD,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;sFAWhC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;;wFAAU;wFAAY,QAAQ,CAAC,EAAE;wFAAC;;;;;;;8FACnD,6LAAC,qIAAA,CAAA,SAAM;oFACL,OAAO;oFACP,eAAe;oFACf,KAAK;oFACL,KAAK;oFACL,MAAM;oFACN,WAAU;;;;;;;;;;;;sFAId,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;;wFAAU;wFAAc,UAAU,CAAC,EAAE;;;;;;;8FACtD,6LAAC,qIAAA,CAAA,SAAM;oFACL,OAAO;oFACP,eAAe;oFACf,KAAK;oFACL,KAAK;oFACL,MAAM;oFACN,WAAU;;;;;;;;;;;;sFAId,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC,qIAAA,CAAA,SAAM;oFAAC,OAAO;oFAAY,eAAe;;sGACxC,6LAAC,qIAAA,CAAA,gBAAa;4FAAC,WAAU;sGACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sGAEd,6LAAC,qIAAA,CAAA,gBAAa;;8GACZ,6LAAC,qIAAA,CAAA,aAAU;oGAAC,OAAM;8GAAM;;;;;;8GACxB,6LAAC,qIAAA,CAAA,aAAU;oGAAC,OAAM;8GAAM;;;;;;8GACxB,6LAAC,qIAAA,CAAA,aAAU;oGAAC,OAAM;8GAAM;;;;;;8GACxB,6LAAC,qIAAA,CAAA,aAAU;oGAAC,OAAM;8GAAM;;;;;;8GACxB,6LAAC,qIAAA,CAAA,aAAU;oGAAC,OAAM;8GAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8EAOhC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,WAAU;sFAAwB;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC,qIAAA,CAAA,SAAM;oFAAC,OAAO;oFAAY,eAAe;;sGACxC,6LAAC,qIAAA,CAAA,gBAAa;4FAAC,WAAU;sGACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sGAEd,6LAAC,qIAAA,CAAA,gBAAa;sGACX,eAAe,GAAG,CAAC,CAAC,0BACnB,6LAAC,qIAAA,CAAA,aAAU;oGAAoB,OAAO,UAAU,EAAE;8GAChD,cAAA,6LAAC;;0HACC,6LAAC;gHAAI,WAAU;0HAAuB,UAAU,IAAI;;;;;;0HACpD,6LAAC;gHAAI,WAAU;0HAAiC,UAAU,WAAW;;;;;;;;;;;;mGAHxD,UAAU,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8EAavC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,WAAU;sFAAwB;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,6MAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;sGACpB,6LAAC,oIAAA,CAAA,QAAK;4FACJ,OAAO;4FACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4FAC3C,aAAY;4FACZ,WAAU;;;;;;;;;;;;8FAId,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,2MAAA,CAAA,UAAO;4FAAC,WAAU;;;;;;sGACnB,6LAAC,oIAAA,CAAA,QAAK;4FACJ,OAAO;4FACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4FAC1C,aAAY;4FACZ,WAAU;;;;;;;;;;;;8FAId,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,+MAAA,CAAA,YAAS;4FAAC,WAAU;;;;;;sGACrB,6LAAC,oIAAA,CAAA,QAAK;4FACJ,OAAO;4FACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4FAC5C,aAAY;4FACZ,WAAU;;;;;;;;;;;;8FAId,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,6MAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;sGACpB,6LAAC,oIAAA,CAAA,QAAK;4FACJ,OAAO;4FACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4FAC3C,aAAY;4FACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8EAOlB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,WAAU;sFAAwB;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,qMAAA,CAAA,OAAI;4FAAC,WAAU;;;;;;sGAChB,6LAAC,oIAAA,CAAA,QAAK;4FACJ,OAAO;4FACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4FACxC,aAAY;4FACZ,WAAU;;;;;;;;;;;;8FAId,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,uMAAA,CAAA,QAAK;4FAAC,WAAU;;;;;;sGACjB,6LAAC,oIAAA,CAAA,QAAK;4FACJ,OAAO;4FACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4FACxC,aAAY;4FACZ,WAAU;;;;;;;;;;;;8FAId,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,6MAAA,CAAA,SAAM;4FAAC,WAAU;;;;;;sGAClB,6LAAC,oIAAA,CAAA,QAAK;4FACJ,OAAO;4FACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4FAC1C,aAAY;4FACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8EAOlB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,WAAU;sFAAwB;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC,oIAAA,CAAA,QAAK;oFACJ,OAAO;oFACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oFAC5C,aAAY;oFACZ,WAAU;;;;;;;;;;;;sFAId,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC,uIAAA,CAAA,WAAQ;oFACP,OAAO;oFACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oFAClD,aAAY;oFACZ,WAAU;;;;;;;;;;;;sFAId,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAU;8FAAU;;;;;;8FAC3B,6LAAC,uIAAA,CAAA,WAAQ;oFACP,OAAO;oFACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oFAC5C,aAAY;oFACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAQrB,CAAC,sBACA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;;0EACnB,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAG7B,6LAAC,mIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;0DAInB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;;wDAChC,kBAAkB,YAAY;wDAC9B,kBAAkB,YAAY;wDAC9B,kBAAkB,aAAa;;;;;;;;;;;;;;;;;;;;;;;8CAKxC,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CACC,WAAW,CAAC;;kBAEV,EAAE,kBAAkB,WAAW,kBAAkB,GAAG;kBACpD,EAAE,kBAAkB,WAAW,kBAAkB,GAAG;kBACpD,EAAE,kBAAkB,YAAY,eAAe,GAAG;kBAClD,EAAE,WAAW,gCAAgC,WAAW;gBAC1D,CAAC;4CACD,OAAO;gDACL,YAAY,eAAe,UAAU,sBAC1B,eAAe,WAAW,uBAC1B,eAAe,aAAa,0BAC5B,eAAe,SAAS,qBACxB,eAAe,eAAe,2BAC9B,eAAe,aAAa,4BAC5B,eAAe,iBAAiB,wBAChC,eAAe,YAAY,wBAAwB;gDAC9D,UAAU,GAAG,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;gDAC5B,YAAY,UAAU,CAAC,EAAE;gDACzB,YAAY;gDACZ,iBAAiB,WAAW,YAAY;gDACxC,OAAO,WAAW,YAAY;gDAC9B,mBAAmB;gDACnB,qBAAqB;gDACrB,kBAAkB;4CACpB;;8DAGA,6LAAC;oDAAI,WAAU;oDAAe,OAAO;wDAAE,aAAa;oDAAa;8DAC/D,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAoB,OAAO;oEAAE,OAAO;gEAAa;0EAC7D;;;;;;0EAEH,6LAAC;gEAAI,WAAW,CAAC,WAAW,EAAE,kBAAkB,WAAW,WAAW,IAAI;;kFACxE,6LAAC;wEAAK,WAAU;kFAAkC;;;;;;kFAClD,6LAAC;wEAAK,WAAU;kFAAkC;;;;;;kFAClD,6LAAC;wEAAK,WAAU;kFAAkC;;;;;;kFAClD,6LAAC;wEAAK,WAAU;kFAAkC;;;;;;;;;;;;4DAEnD,kBAAkB,0BACjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;8DAOvB,6LAAC;oDAAI,WAAU;oDAAM,OAAO;wDAAE,iBAAiB,GAAG,aAAa,EAAE,CAAC;oDAAC;8DACjE,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO;oEACP,UAAU,kBAAkB,WAAW,SAAS,kBAAkB,WAAW,WAAW;gEAC1F;0EAEC;;;;;;0EAEH,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,OAAO;oEACP,UAAU,kBAAkB,WAAW,SAAS;gEAClD;0EAEC;;;;;;0EAEH,6LAAC;gEAAE,WAAU;0EACV;;;;;;0EAEH,6LAAC;gEACC,WAAU;gEACV,OAAO;oEACL,iBAAiB;oEACjB,UAAU,kBAAkB,WAAW,aAAa;gEACtD;0EACD;;;;;;;;;;;;;;;;;8DAOL,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO;4DAAa;sEAC9B;;;;;;sEAGD,6LAAC;4DAAI,WAAW,CAAC,WAAW,EAAE,kBAAkB,WAAW,gBAAgB,kBAAkB,WAAW,gBAAgB,eAAe;sEACpI;gEAAC;gEAAG;gEAAG;6DAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC;oEAEC,WAAU;oEACV,OAAO;wEACL,aAAa;wEACb,iBAAiB,WAAW,YAAY;oEAC1C;;sFAEA,6LAAC;4EACC,WAAU;4EACV,OAAO;gFAAE,iBAAiB;4EAAY;sFAEtC,cAAA,6LAAC,mMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;;;;;;sFAEjB,6LAAC;4EAAG,WAAU;;gFAAqB;gFAAS;;;;;;;sFAC5C,6LAAC;4EAAE,WAAU;sFAAqB;;;;;;;mEAd7B;;;;;;;;;;;;;;;;gDAuBZ,CAAC,SAAS,SAAS,OAAO,mBACzB,6LAAC;oDAAI,WAAU;oDAAM,OAAO;wDAAE,iBAAiB,GAAG,eAAe,EAAE,CAAC;oDAAC;;sEACnE,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,OAAO;4DAAe;sEAChC;;;;;;sEAGD,6LAAC;4DAAI,WAAW,CAAC,WAAW,EAAE,kBAAkB,WAAW,gBAAgB,eAAe;;gEACvF,uBACC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;4EAAU,OAAO;gFAAE,OAAO;4EAAa;;;;;;sFACvD,6LAAC;4EAAK,WAAU;sFAAW;;;;;;;;;;;;gEAG9B,uBACC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;4EAAU,OAAO;gFAAE,OAAO;4EAAa;;;;;;sFACxD,6LAAC;4EAAK,WAAU;sFAAW;;;;;;;;;;;;gEAG9B,yBACC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;4EAAU,OAAO;gFAAE,OAAO;4EAAa;;;;;;sFACzD,6LAAC;4EAAK,WAAU;sFAAW;;;;;;;;;;;;;;;;;;;;;;;;gDAQpC,CAAC,YAAY,WAAW,aAAa,QAAQ,mBAC5C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,6LAAC;4DAAI,WAAU;;gEACZ,0BACC,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB;oEAAa;8EAEvC,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;gEAGvB,yBACC,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB;oEAAa;8EAEvC,cAAA,6LAAC,2MAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;;;;;;gEAGtB,2BACC,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB;oEAAa;8EAEvC,cAAA,6LAAC,+MAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;gEAGxB,0BACC,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB;oEAAa;8EAEvC,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8DAQ9B,6LAAC;oDACC,WAAU;oDACV,OAAO;wDACL,aAAa;wDACb,iBAAiB,WAAW,YAAY;oDAC1C;;sEAEA,6LAAC;4DAAE,WAAU;;gEAAqB;gEACxB;gEAAS;;;;;;;wDAElB,CAAC,YAAY,WAAW,aAAa,QAAQ,mBAC5C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAqB;;;;;;gEACpC,0BAAY,6LAAC;oEAAK,WAAU;8EAA0C;;;;;;gEACtE,yBAAW,6LAAC;oEAAK,WAAU;8EAA0C;;;;;;gEACrE,2BAAa,6LAAC;oEAAK,WAAU;8EAA0C;;;;;;gEACvE,0BAAY,6LAAC;oEAAK,WAAU;8EAA0C;;;;;;;;;;;;;;;;;;;;;;;;sDAO/E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAqB;;;;;;8DACnC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,6LAAC;oEAAE,WAAU;8EAAe;;;;;;;;;;;;sEAE9B,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,6LAAC;oEAAE,WAAU;;wEAAe;wEAAY;wEAAE,YAAY;;;;;;;;;;;;;sEAExD,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,6LAAC;oEAAE,WAAU;;wEAAe;wEAAW;wEAAE,QAAQ,CAAC,EAAE;wEAAC;;;;;;;;;;;;;sEAEvD,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAwB;;;;;;8EACxC,6LAAC;oEAAE,WAAU;8EAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU1C,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;;sDACL,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;;sDACd,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;;sDACd,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD;GAjtCwB;KAAA", "debugId": null}}]}