{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  const url = process.env.NEXT_PUBLIC_SUPABASE_URL\n  const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n  if (!url || !key) {\n    throw new Error('Missing Supabase environment variables. Please check your .env.local file.')\n  }\n\n  return createBrowserClient(url, key)\n}\n"], "names": [], "mappings": ";;;AAGc;AAHd;AAAA;;AAEO,SAAS;IACd,MAAM;IACN,MAAM;IAEN,uCAAkB;;IAElB;IAEA,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK;AAClC", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/hooks/use-auth.ts"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect, useCallback, useMemo } from 'react'\nimport { createClient } from '@/lib/supabase/client'\nimport { User } from '@supabase/supabase-js'\n\ninterface UserProfile {\n  id: string;\n  full_name?: string;\n  role?: string;\n}\n\nexport function useAuth() {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<UserProfile | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [initialized, setInitialized] = useState(false)\n\n  const supabase = useMemo(() => createClient(), [])\n\n  const loadUserProfile = useCallback(async (user: User) => {\n    try {\n      const { data: profile, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', user.id)\n        .single()\n\n      if (error && error.code !== 'PGRST116') {\n        console.error('Error loading profile:', error)\n        throw error\n      }\n\n      setProfile(profile)\n    } catch (error) {\n      console.error('Error loading profile:', error)\n    }\n  }, [supabase])\n\n  useEffect(() => {\n    let mounted = true\n\n    const initAuth = async () => {\n      try {\n        const { data: { user }, error } = await supabase.auth.getUser()\n\n        if (error) {\n          console.error('Error checking user:', error)\n          throw error\n        }\n\n        if (mounted) {\n          if (user) {\n            setUser(user)\n            await loadUserProfile(user)\n          } else {\n            setUser(null)\n            setProfile(null)\n          }\n          setLoading(false)\n          setInitialized(true)\n        }\n      } catch (error) {\n        console.error('Error in initAuth:', error)\n        if (mounted) {\n          setUser(null)\n          setProfile(null)\n          setLoading(false)\n          setInitialized(true)\n        }\n      }\n    }\n\n    // Only run once on mount\n    if (!initialized) {\n      initAuth()\n    }\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (!mounted) return\n\n        console.log('Auth state change:', event)\n\n        if (event === 'SIGNED_IN' && session?.user) {\n          setLoading(true)\n          setUser(session.user)\n          await loadUserProfile(session.user)\n          if (mounted) setLoading(false)\n        } else if (event === 'SIGNED_OUT') {\n          setUser(null)\n          setProfile(null)\n          setLoading(false)\n        } else if (event === 'TOKEN_REFRESHED' && session?.user) {\n          setUser(session.user)\n          // Don't set loading for token refresh\n        }\n      }\n    )\n\n    return () => {\n      mounted = false\n      subscription.unsubscribe()\n    }\n  }, [initialized]) // Only depend on initialized\n\n  const signOut = useCallback(async () => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signOut()\n      if (error) throw error\n      \n      setUser(null)\n      setProfile(null)\n    } catch (error) {\n      console.error('Error signing out:', error)\n      throw error\n    } finally {\n      setLoading(false)\n    }\n  }, [supabase])\n\n  return {\n    user,\n    profile,\n    loading,\n    initialized,\n    signOut\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAYO,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qCAAE,IAAM,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;oCAAK,EAAE;IAEjD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,OAAO;YACzC,IAAI;gBACF,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;gBAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY;oBACtC,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,MAAM;gBACR;gBAEA,WAAW;YACb,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;QACF;+CAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,UAAU;YAEd,MAAM;8CAAW;oBACf,IAAI;wBACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;wBAE7D,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,wBAAwB;4BACtC,MAAM;wBACR;wBAEA,IAAI,SAAS;4BACX,IAAI,MAAM;gCACR,QAAQ;gCACR,MAAM,gBAAgB;4BACxB,OAAO;gCACL,QAAQ;gCACR,WAAW;4BACb;4BACA,WAAW;4BACX,eAAe;wBACjB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,sBAAsB;wBACpC,IAAI,SAAS;4BACX,QAAQ;4BACR,WAAW;4BACX,WAAW;4BACX,eAAe;wBACjB;oBACF;gBACF;;YAEA,yBAAyB;YACzB,IAAI,CAAC,aAAa;gBAChB;YACF;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;qCAChE,OAAO,OAAO;oBACZ,IAAI,CAAC,SAAS;oBAEd,QAAQ,GAAG,CAAC,sBAAsB;oBAElC,IAAI,UAAU,eAAe,SAAS,MAAM;wBAC1C,WAAW;wBACX,QAAQ,QAAQ,IAAI;wBACpB,MAAM,gBAAgB,QAAQ,IAAI;wBAClC,IAAI,SAAS,WAAW;oBAC1B,OAAO,IAAI,UAAU,cAAc;wBACjC,QAAQ;wBACR,WAAW;wBACX,WAAW;oBACb,OAAO,IAAI,UAAU,qBAAqB,SAAS,MAAM;wBACvD,QAAQ,QAAQ,IAAI;oBACpB,sCAAsC;oBACxC;gBACF;;YAGF;qCAAO;oBACL,UAAU;oBACV,aAAa,WAAW;gBAC1B;;QACF;4BAAG;QAAC;KAAY,EAAE,6BAA6B;;IAE/C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wCAAE;YAC1B,IAAI;gBACF,WAAW;gBACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;gBAC7C,IAAI,OAAO,MAAM;gBAEjB,QAAQ;gBACR,WAAW;YACb,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;uCAAG;QAAC;KAAS;IAEb,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;GAtHgB", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 499, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,+KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,+KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,+KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,+KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,+KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,+KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,+KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,+KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,+KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,6LAAC,+KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,+KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,+KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/components/navigation/horizontal-nav.tsx"], "sourcesContent": ["\"use client\"\n\nimport Link from \"next/link\"\nimport { usePathname, useRouter } from \"next/navigation\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Badge } from \"@/components/ui/badge\"\nimport {\n  Home,\n  FileText,\n  Palette,\n  Mail,\n  LogOut,\n  LogIn,\n  User,\n  Settings,\n  Shield,\n  LayoutDashboard,\n  Menu\n} from \"lucide-react\"\nimport { useAuth } from \"@/hooks/use-auth\"\nimport { toast } from \"sonner\"\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\"\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\"\n\nconst navigation = [\n  {\n    name: \"Home\",\n    href: \"/\",\n    icon: Home,\n  },\n  {\n    name: \"Templates\",\n    href: \"/templates\",\n    icon: FileText,\n  },\n  {\n    name: \"Customize\",\n    href: \"/customize\",\n    icon: Palette,\n  },\n  {\n    name: \"<PERSON>\",\n    href: \"/contact\",\n    icon: Mail,\n  },\n]\n\ninterface HorizontalNavProps {\n  className?: string\n}\n\nexport function HorizontalNav({ className }: HorizontalNavProps) {\n  const pathname = usePathname()\n  const router = useRouter()\n  const { user, profile, loading, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    try {\n      await signOut()\n      toast.success('Signed out successfully')\n\n      // Add a small delay before redirecting to allow state update to render\n      setTimeout(() => {\n        router.push('/')\n      }, 50); // 50ms delay\n\n    } catch (error) {\n      console.error('Sign out error:', error)\n      toast.error('Failed to sign out')\n    }\n  }\n\n  // Filter navigation based on user role\n  const getFilteredNavigation = () => {\n    const nav = [...navigation]\n    return nav\n  }\n\n  return (\n    <nav className={cn(\"bg-card border-b\", className)}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center gap-4\">\n            <Link href=\"/\" className=\"flex items-center gap-2\">\n              <h1 className=\"text-xl font-bold\">KaleidoneX</h1>\n            </Link>\n            {profile?.role === 'admin' && (\n              <Badge variant=\"secondary\" className=\"text-xs\">\n                <Shield className=\"h-3 w-3 mr-1\" />\n                Admin\n              </Badge>\n            )}\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {getFilteredNavigation().map((item) => {\n              const isActive = pathname === item.href\n              return (\n                <Link key={item.name} href={item.href}>\n                  <Button\n                    variant={isActive ? \"secondary\" : \"ghost\"}\n                    size=\"sm\"\n                    className={cn(\n                      \"gap-2\",\n                      isActive && \"bg-secondary\"\n                    )}\n                  >\n                    <item.icon className=\"h-4 w-4\" />\n                    {item.name}\n                    {item.name === \"Admin Panel\" && (\n                      <Badge variant=\"outline\" className=\"ml-1 text-xs\">\n                        Admin\n                      </Badge>\n                    )}\n                  </Button>\n                </Link>\n              )\n            })}\n          </div>\n\n          {/* User section */}\n          <div className=\"flex items-center space-x-4\">\n            {loading ? (\n              <div className=\"flex items-center gap-2\">\n                <div className=\"w-4 h-4 border-2 border-muted border-t-primary rounded-full animate-spin\" />\n                <span className=\"text-sm text-muted-foreground\">Loading...</span>\n              </div>\n            ) : user ? (\n              <DropdownMenu>\n                <DropdownMenuTrigger asChild>\n                  <Button variant=\"ghost\" className=\"flex items-center gap-2\">\n                    <div className=\"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center\">\n                      <User className=\"h-4 w-4\" />\n                    </div>\n                    <span className=\"hidden sm:block text-sm font-medium\">\n                      {profile?.full_name || user.email?.split('@')[0] || 'User'}\n                    </span>\n                  </Button>\n                </DropdownMenuTrigger>\n                <DropdownMenuContent align=\"end\" className=\"w-56\">\n                  <div className=\"flex items-center gap-2 p-2\">\n                    <div className=\"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center\">\n                      <User className=\"h-4 w-4\" />\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <p className=\"text-sm font-medium truncate\">\n                        {profile?.full_name || user.email?.split('@')[0] || 'User'}\n                      </p>\n                      <p className=\"text-xs text-muted-foreground truncate\">\n                        {user.email}\n                      </p>\n                    </div>\n                  </div>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard\" className=\"flex items-center gap-2\">\n                      <LayoutDashboard className=\"h-4 w-4\" />\n                      Dashboard\n                    </Link>\n                  </DropdownMenuItem>\n                  {profile?.role === 'admin' && (\n                    <DropdownMenuItem asChild>\n                      <Link href=\"/admin\" className=\"flex items-center gap-2\">\n                        <Shield className=\"h-4 w-4\" />\n                        Admin Panel\n                      </Link>\n                    </DropdownMenuItem>\n                  )}\n                  <DropdownMenuItem asChild>\n                    <Link href=\"/dashboard\" className=\"flex items-center gap-2\">\n                      <Settings className=\"h-4 w-4\" />\n                      Settings\n                    </Link>\n                  </DropdownMenuItem>\n                  <DropdownMenuSeparator />\n                  <DropdownMenuItem\n                    onClick={handleSignOut}\n                    className=\"text-red-600 focus:text-red-700 focus:bg-red-50\"\n                  >\n                    <LogOut className=\"h-4 w-4 mr-2\" />\n                    Sign Out\n                  </DropdownMenuItem>\n                </DropdownMenuContent>\n              </DropdownMenu>\n            ) : (\n              <Link href=\"/login\">\n                <Button className=\"flex items-center gap-2\">\n                  <LogIn className=\"h-4 w-4\" />\n                  Sign In\n                </Button>\n              </Link>\n            )}\n          </div>\n\n          {/* Mobile menu */}\n          <div className=\"md:hidden\">\n            <Sheet>\n              <SheetTrigger asChild>\n                <Button variant=\"ghost\" size=\"icon\">\n                  <Menu className=\"h-5 w-5\" />\n                </Button>\n              </SheetTrigger>\n              <SheetContent side=\"right\" className=\"w-64\">\n                <div className=\"flex flex-col h-full\">\n                  {/* Mobile Logo */}\n                  <div className=\"flex items-center gap-2 px-4 py-4 border-b\">\n                    <h1 className=\"text-xl font-bold\">KaleidoneX</h1>\n                    {profile?.role === 'admin' && (\n                      <Badge variant=\"secondary\" className=\"text-xs\">\n                        <Shield className=\"h-3 w-3 mr-1\" />\n                        Admin\n                      </Badge>\n                    )}\n                  </div>\n\n                  {/* Mobile Navigation */}\n                  <nav className=\"flex-1 px-4 py-6 space-y-2\">\n                    {getFilteredNavigation().map((item) => {\n                      const isActive = pathname === item.href\n                      return (\n                        <Link key={item.name} href={item.href}>\n                          <Button\n                            variant={isActive ? \"secondary\" : \"ghost\"}\n                            className={cn(\n                              \"w-full justify-start gap-3\",\n                              isActive && \"bg-secondary\"\n                            )}\n                          >\n                            <item.icon className=\"h-4 w-4\" />\n                            {item.name}\n                            {item.name === \"Admin Panel\" && (\n                              <Badge variant=\"outline\" className=\"ml-auto text-xs\">\n                                Admin\n                              </Badge>\n                            )}\n                          </Button>\n                        </Link>\n                      )\n                    })}\n                  </nav>\n\n                  {/* Mobile User section */}\n                  <div className=\"px-4 py-4 border-t\">\n                    {user ? (\n                      <div className=\"space-y-2\">\n                        <div className=\"flex items-center gap-3 px-3 py-2 rounded-md bg-muted/50\">\n                          <div className=\"w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center\">\n                            <User className=\"h-4 w-4\" />\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <p className=\"text-sm font-medium truncate\">\n                              {profile?.full_name || user.email?.split('@')[0] || 'User'}\n                            </p>\n                            <p className=\"text-xs text-muted-foreground truncate\">\n                              {user.email}\n                            </p>\n                          </div>\n                        </div>\n                        <div className=\"space-y-1\">\n                          <Link href=\"/dashboard\">\n                            <Button variant=\"ghost\" size=\"sm\" className=\"w-full justify-start gap-3\">\n                              <LayoutDashboard className=\"h-4 w-4\" />\n                              Dashboard\n                            </Button>\n                          </Link>\n                          {profile?.role === 'admin' && (\n                            <Link href=\"/admin\">\n                              <Button variant=\"ghost\" size=\"sm\" className=\"w-full justify-start gap-3\">\n                                <Shield className=\"h-4 w-4\" />\n                                Admin Panel\n                              </Button>\n                            </Link>\n                          )}\n                          <Link href=\"/dashboard\">\n                            <Button variant=\"ghost\" size=\"sm\" className=\"w-full justify-start gap-3\">\n                              <Settings className=\"h-4 w-4\" />\n                              Settings\n                            </Button>\n                          </Link>\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"w-full justify-start gap-3 text-red-600 hover:text-red-700 hover:bg-red-50\"\n                            onClick={handleSignOut}\n                          >\n                            <LogOut className=\"h-4 w-4\" />\n                            Sign Out\n                          </Button>\n                        </div>\n                      </div>\n                    ) : (\n                      <Link href=\"/login\">\n                        <Button className=\"w-full justify-start gap-3\">\n                          <LogIn className=\"h-4 w-4\" />\n                          Sign In\n                        </Button>\n                      </Link>\n                    )}\n                  </div>\n                </div>\n              </SheetContent>\n            </Sheet>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;;;AAvBA;;;;;;;;;;;AA+BA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,OAAI;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,iNAAA,CAAA,WAAQ;IAChB;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;IACZ;CACD;AAMM,SAAS,cAAc,EAAE,SAAS,EAAsB;;IAC7D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IAElD,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YACN,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,uEAAuE;YACvE,WAAW;gBACT,OAAO,IAAI,CAAC;YACd,GAAG,KAAK,aAAa;QAEvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,uCAAuC;IACvC,MAAM,wBAAwB;QAC5B,MAAM,MAAM;eAAI;SAAW;QAC3B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;kBACrC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC;oCAAG,WAAU;8CAAoB;;;;;;;;;;;4BAEnC,SAAS,SAAS,yBACjB,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;;kDACnC,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAOzC,6LAAC;wBAAI,WAAU;kCACZ,wBAAwB,GAAG,CAAC,CAAC;4BAC5B,MAAM,WAAW,aAAa,KAAK,IAAI;4BACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CACnC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,WAAW,cAAc;oCAClC,MAAK;oCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,SACA,YAAY;;sDAGd,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;wCACpB,KAAK,IAAI;wCACT,KAAK,IAAI,KAAK,+BACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAe;;;;;;;;;;;;+BAZ7C,KAAK,IAAI;;;;;wBAmBxB;;;;;;kCAIF,6LAAC;wBAAI,WAAU;kCACZ,wBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;mCAEhD,qBACF,6LAAC,+IAAA,CAAA,eAAY;;8CACX,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;;0DAChC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDAAK,WAAU;0DACb,SAAS,aAAa,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;;;;;;;;;;;;8CAI1D,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEACV,SAAS,aAAa,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;sEAEtD,6LAAC;4DAAE,WAAU;sEACV,KAAK,KAAK;;;;;;;;;;;;;;;;;;sDAIjB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sDACtB,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;;kEAChC,6LAAC,+NAAA,CAAA,kBAAe;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;wCAI1C,SAAS,SAAS,yBACjB,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;;kEAC5B,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAKpC,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,OAAO;sDACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;;kEAChC,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;sDAIpC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sDACtB,6LAAC,+IAAA,CAAA,mBAAgB;4CACf,SAAS;4CACT,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;iDAMzC,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,6LAAC,2MAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;kCAQrC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;8CACJ,6LAAC,oIAAA,CAAA,eAAY;oCAAC,OAAO;8CACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAGpB,6LAAC,oIAAA,CAAA,eAAY;oCAAC,MAAK;oCAAQ,WAAU;8CACnC,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoB;;;;;;oDACjC,SAAS,SAAS,yBACjB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;;0EACnC,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAOzC,6LAAC;gDAAI,WAAU;0DACZ,wBAAwB,GAAG,CAAC,CAAC;oDAC5B,MAAM,WAAW,aAAa,KAAK,IAAI;oDACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wDAAiB,MAAM,KAAK,IAAI;kEACnC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAS,WAAW,cAAc;4DAClC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8BACA,YAAY;;8EAGd,6LAAC,KAAK,IAAI;oEAAC,WAAU;;;;;;gEACpB,KAAK,IAAI;gEACT,KAAK,IAAI,KAAK,+BACb,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;8EAAkB;;;;;;;;;;;;uDAXhD,KAAK,IAAI;;;;;gDAkBxB;;;;;;0DAIF,6LAAC;gDAAI,WAAU;0DACZ,qBACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFACV,SAAS,aAAa,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;sFAEtD,6LAAC;4EAAE,WAAU;sFACV,KAAK,KAAK;;;;;;;;;;;;;;;;;;sEAIjB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;8EACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;wEAAK,WAAU;;0FAC1C,6LAAC,+NAAA,CAAA,kBAAe;gFAAC,WAAU;;;;;;4EAAY;;;;;;;;;;;;gEAI1C,SAAS,SAAS,yBACjB,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;8EACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;wEAAK,WAAU;;0FAC1C,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAY;;;;;;;;;;;;8EAKpC,6LAAC,+JAAA,CAAA,UAAI;oEAAC,MAAK;8EACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;wEAAK,WAAU;;0FAC1C,6LAAC,6MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EAAY;;;;;;;;;;;;8EAIpC,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS;;sFAET,6LAAC,6MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAY;;;;;;;;;;;;;;;;;;yEAMpC,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,6LAAC,2MAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcvD;GAlQgB;;QACG,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACoB,8HAAA,CAAA,UAAO;;;KAHrC", "debugId": null}}, {"offset": {"line": 1530, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/hooks/use-visitor-tracking.ts"], "sourcesContent": ["\"use client\"\n\nimport { useEffect, useState } from 'react'\nimport { usePathname } from 'next/navigation'\n\n// Generate a session ID that persists for the browser session\nconst getSessionId = () => {\n  if (typeof window === 'undefined') return 'server'\n\n  let sessionId = sessionStorage.getItem('visitor_session_id')\n  if (!sessionId) {\n    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n    sessionStorage.setItem('visitor_session_id', sessionId)\n  }\n  return sessionId\n}\n\n// Detect device type\nconst getDeviceType = () => {\n  if (typeof window === 'undefined') return 'unknown'\n\n  const userAgent = navigator.userAgent.toLowerCase()\n  if (/tablet|ipad|playbook|silk/.test(userAgent)) return 'tablet'\n  if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\\sce|palm|smartphone|iemobile/.test(userAgent)) return 'mobile'\n  return 'desktop'\n}\n\n// Detect browser\nconst getBrowser = () => {\n  if (typeof window === 'undefined') return 'unknown'\n\n  const userAgent = navigator.userAgent\n  if (userAgent.includes('Chrome')) return 'Chrome'\n  if (userAgent.includes('Firefox')) return 'Firefox'\n  if (userAgent.includes('Safari')) return 'Safari'\n  if (userAgent.includes('Edge')) return 'Edge'\n  if (userAgent.includes('Opera')) return 'Opera'\n  return 'Other'\n}\n\n// Detect operating system\nconst getOperatingSystem = () => {\n  if (typeof window === 'undefined') return 'unknown'\n\n  const userAgent = navigator.userAgent\n  if (userAgent.includes('Windows')) return 'Windows'\n  if (userAgent.includes('Mac')) return 'macOS'\n  if (userAgent.includes('Linux')) return 'Linux'\n  if (userAgent.includes('Android')) return 'Android'\n  if (userAgent.includes('iOS')) return 'iOS'\n  return 'Other'\n}\n\n// Get connection type (if available)\nconst getConnectionType = () => {\n  if (typeof window === 'undefined') return 'unknown'\n\n  // @ts-ignore - navigator.connection is experimental\n  const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection\n  return connection?.effectiveType || 'unknown'\n}\n\n// Get approximate location (using timezone)\nconst getLocationInfo = () => {\n  if (typeof window === 'undefined') return { country: 'unknown', city: 'unknown' }\n\n  try {\n    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone\n    // Simple mapping of timezones to countries (basic implementation)\n    const timezoneToCountry: Record<string, string> = {\n      'America/New_York': 'United States',\n      'America/Los_Angeles': 'United States',\n      'Europe/London': 'United Kingdom',\n      'Europe/Paris': 'France',\n      'Europe/Berlin': 'Germany',\n      'Asia/Tokyo': 'Japan',\n      'Asia/Shanghai': 'China',\n      'Asia/Kolkata': 'India',\n      'Australia/Sydney': 'Australia',\n    }\n\n    const country = timezoneToCountry[timezone] || 'Unknown'\n    const city = timezone.split('/')[1]?.replace('_', ' ') || 'Unknown'\n\n    return { country, city }\n  } catch {\n    return { country: 'unknown', city: 'unknown' }\n  }\n}\n\nexport function useVisitorTracking() {\n  const pathname = usePathname()\n  const [pageLoadTime, setPageLoadTime] = useState<number | null>(null)\n\n  useEffect(() => {\n    // Measure page load time\n    const startTime = performance.now()\n\n    const handleLoad = () => {\n      const loadTime = Math.round(performance.now() - startTime)\n      setPageLoadTime(loadTime)\n    }\n\n    if (document.readyState === 'complete') {\n      handleLoad()\n    } else {\n      window.addEventListener('load', handleLoad)\n      return () => window.removeEventListener('load', handleLoad)\n    }\n  }, [])\n\n  useEffect(() => {\n    const trackVisitor = async () => {\n      try {\n        if (typeof window === 'undefined') return\n\n        const { country, city } = getLocationInfo()\n\n        const trackingData = {\n          path: pathname,\n          userAgent: navigator.userAgent,\n          referrer: document.referrer,\n          screenResolution: `${screen.width}x${screen.height}`,\n          language: navigator.language,\n          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,\n          sessionId: getSessionId(),\n          deviceType: getDeviceType(),\n          browser: getBrowser(),\n          os: getOperatingSystem(),\n          country,\n          city,\n          pageTitle: document.title,\n          loadTime: pageLoadTime,\n          connectionType: getConnectionType(),\n          timestamp: new Date().toISOString(),\n        }\n\n        await fetch('/api/track-visitor', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(trackingData),\n        })\n      } catch (error) {\n        // Silently fail - visitor tracking shouldn't break the app\n        console.warn('Failed to track visitor:', error)\n      }\n    }\n\n    // Add a small delay to ensure page is fully loaded\n    const timer = setTimeout(trackVisitor, 1000)\n    return () => clearTimeout(timer)\n  }, [pathname, pageLoadTime])\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAKA,8DAA8D;AAC9D,MAAM,eAAe;IACnB,uCAAmC;;IAAc;IAEjD,IAAI,YAAY,eAAe,OAAO,CAAC;IACvC,IAAI,CAAC,WAAW;QACd,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAC9E,eAAe,OAAO,CAAC,sBAAsB;IAC/C;IACA,OAAO;AACT;AAEA,qBAAqB;AACrB,MAAM,gBAAgB;IACpB,uCAAmC;;IAAe;IAElD,MAAM,YAAY,UAAU,SAAS,CAAC,WAAW;IACjD,IAAI,4BAA4B,IAAI,CAAC,YAAY,OAAO;IACxD,IAAI,wFAAwF,IAAI,CAAC,YAAY,OAAO;IACpH,OAAO;AACT;AAEA,iBAAiB;AACjB,MAAM,aAAa;IACjB,uCAAmC;;IAAe;IAElD,MAAM,YAAY,UAAU,SAAS;IACrC,IAAI,UAAU,QAAQ,CAAC,WAAW,OAAO;IACzC,IAAI,UAAU,QAAQ,CAAC,YAAY,OAAO;IAC1C,IAAI,UAAU,QAAQ,CAAC,WAAW,OAAO;IACzC,IAAI,UAAU,QAAQ,CAAC,SAAS,OAAO;IACvC,IAAI,UAAU,QAAQ,CAAC,UAAU,OAAO;IACxC,OAAO;AACT;AAEA,0BAA0B;AAC1B,MAAM,qBAAqB;IACzB,uCAAmC;;IAAe;IAElD,MAAM,YAAY,UAAU,SAAS;IACrC,IAAI,UAAU,QAAQ,CAAC,YAAY,OAAO;IAC1C,IAAI,UAAU,QAAQ,CAAC,QAAQ,OAAO;IACtC,IAAI,UAAU,QAAQ,CAAC,UAAU,OAAO;IACxC,IAAI,UAAU,QAAQ,CAAC,YAAY,OAAO;IAC1C,IAAI,UAAU,QAAQ,CAAC,QAAQ,OAAO;IACtC,OAAO;AACT;AAEA,qCAAqC;AACrC,MAAM,oBAAoB;IACxB,uCAAmC;;IAAe;IAElD,oDAAoD;IACpD,MAAM,aAAa,UAAU,UAAU,IAAI,UAAU,aAAa,IAAI,UAAU,gBAAgB;IAChG,OAAO,YAAY,iBAAiB;AACtC;AAEA,4CAA4C;AAC5C,MAAM,kBAAkB;IACtB,uCAAmC;;IAA6C;IAEhF,IAAI;QACF,MAAM,WAAW,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;QACjE,kEAAkE;QAClE,MAAM,oBAA4C;YAChD,oBAAoB;YACpB,uBAAuB;YACvB,iBAAiB;YACjB,gBAAgB;YAChB,iBAAiB;YACjB,cAAc;YACd,iBAAiB;YACjB,gBAAgB;YAChB,oBAAoB;QACtB;QAEA,MAAM,UAAU,iBAAiB,CAAC,SAAS,IAAI;QAC/C,MAAM,OAAO,SAAS,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,KAAK,QAAQ;QAE1D,OAAO;YAAE;YAAS;QAAK;IACzB,EAAE,OAAM;QACN,OAAO;YAAE,SAAS;YAAW,MAAM;QAAU;IAC/C;AACF;AAEO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,yBAAyB;YACzB,MAAM,YAAY,YAAY,GAAG;YAEjC,MAAM;2DAAa;oBACjB,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY,GAAG,KAAK;oBAChD,gBAAgB;gBAClB;;YAEA,IAAI,SAAS,UAAU,KAAK,YAAY;gBACtC;YACF,OAAO;gBACL,OAAO,gBAAgB,CAAC,QAAQ;gBAChC;oDAAO,IAAM,OAAO,mBAAmB,CAAC,QAAQ;;YAClD;QACF;uCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM;6DAAe;oBACnB,IAAI;wBACF,uCAAmC;;wBAAK;wBAExC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG;wBAE1B,MAAM,eAAe;4BACnB,MAAM;4BACN,WAAW,UAAU,SAAS;4BAC9B,UAAU,SAAS,QAAQ;4BAC3B,kBAAkB,GAAG,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE;4BACpD,UAAU,UAAU,QAAQ;4BAC5B,UAAU,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;4BAC1D,WAAW;4BACX,YAAY;4BACZ,SAAS;4BACT,IAAI;4BACJ;4BACA;4BACA,WAAW,SAAS,KAAK;4BACzB,UAAU;4BACV,gBAAgB;4BAChB,WAAW,IAAI,OAAO,WAAW;wBACnC;wBAEA,MAAM,MAAM,sBAAsB;4BAChC,QAAQ;4BACR,SAAS;gCACP,gBAAgB;4BAClB;4BACA,MAAM,KAAK,SAAS,CAAC;wBACvB;oBACF,EAAE,OAAO,OAAO;wBACd,2DAA2D;wBAC3D,QAAQ,IAAI,CAAC,4BAA4B;oBAC3C;gBACF;;YAEA,mDAAmD;YACnD,MAAM,QAAQ,WAAW,cAAc;YACvC;gDAAO,IAAM,aAAa;;QAC5B;uCAAG;QAAC;QAAU;KAAa;AAC7B;GAhEgB;;QACG,qIAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 1717, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6LAAC,2IAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf;GAjBM;;QACyB,mJAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}, {"offset": {"line": 1764, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/components/layout/main-layout.tsx"], "sourcesContent": ["\"use client\"\n\nimport { HorizontalNav } from \"@/components/navigation/horizontal-nav\"\nimport { useVisitorTracking } from \"@/hooks/use-visitor-tracking\"\nimport { Toaster } from \"@/components/ui/sonner\"\n\ninterface MainLayoutProps {\n  children: React.ReactNode\n}\n\nexport function MainLayout({ children }: MainLayoutProps) {\n  // Track visitor\n  useVisitorTracking()\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Horizontal Navigation */}\n      <HorizontalNav />\n\n      {/* Main Content */}\n      <main className=\"flex-1 p-6\">\n        {children}\n      </main>\n\n      {/* Toast Notifications */}\n      <Toaster />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IACtD,gBAAgB;IAChB,CAAA,GAAA,6IAAA,CAAA,qBAAkB,AAAD;IAEjB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,wJAAA,CAAA,gBAAa;;;;;0BAGd,6LAAC;gBAAK,WAAU;0BACb;;;;;;0BAIH,6LAAC,qIAAA,CAAA,UAAO;;;;;;;;;;;AAGd;GAlBgB;;QAEd,6IAAA,CAAA,qBAAkB;;;KAFJ", "debugId": null}}]}