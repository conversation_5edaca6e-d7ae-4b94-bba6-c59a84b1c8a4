{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_8c6513bb._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_e9c0b249.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HOSbDhNyAn2K5fvwcxXZNSoMw3wKR8L948qkXy/QOCg=", "__NEXT_PREVIEW_MODE_ID": "b293635559e16e839290a14654a89ca2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a82dbd2542b8aad72a6558d7d7b5bb2aac9fc448cea873ca5d9522906d033d04", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "12958a345ec5742a3263548335663cd30e315d25b8b4883ea8fc39e9d4a4e420"}}}, "sortedMiddleware": ["/"], "functions": {}}