{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_8c6513bb._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_e9c0b249.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HOSbDhNyAn2K5fvwcxXZNSoMw3wKR8L948qkXy/QOCg=", "__NEXT_PREVIEW_MODE_ID": "289d51af46d177f1e64954bb48b62330", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0c0eba4395fb497683a68f8479fbc58eecd30de75c87c9d56a36b8b3112ac67a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "32e00f06060208b5c966f6df8e27bf92da4eae48ba0e6b4011e0ba9d6e2f1397"}}}, "instrumentation": null, "functions": {}}