{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_8c6513bb._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_e9c0b249.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HOSbDhNyAn2K5fvwcxXZNSoMw3wKR8L948qkXy/QOCg=", "__NEXT_PREVIEW_MODE_ID": "e0a1993eabca42e3e21653d3ec91fa8e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6bc16b3c1e186d15c50c79f8aa5b0b0ec90179bdeec34c2642bb417082ea1b33", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "aab10d394ac763830baa78f9f3ace79cbec3d5312c0bf425cf8ff1636696f6ce"}}}, "instrumentation": null, "functions": {}}