{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_8c6513bb._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_e9c0b249.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HOSbDhNyAn2K5fvwcxXZNSoMw3wKR8L948qkXy/QOCg=", "__NEXT_PREVIEW_MODE_ID": "6f0a3907f0efe16c022f265771b082a8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0ea41235040cfd008945e466c06f493c3798b17299f0630d7e12cac0196a0dec", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "00ca1181259ba4237398509c2dffa1e1c7311bd4cf32a509e2c0736ee16abbb8"}}}, "instrumentation": null, "functions": {}}