{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\nimport { createClient as createAdminClient } from '@supabase/supabase-js' // Import for database client\nimport { Database } from '@/lib/database.types'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  const url = process.env.NEXT_PUBLIC_SUPABASE_URL\n  const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n  if (!url || !key) {\n    throw new Error('Missing Supabase environment variables. Please check your .env.local file.')\n  }\n\n  // Use createServerClient for server-side database access with cookie handling\n  return createServerClient(\n    url,\n    key,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n\n// New function to create a Supabase client for database operations using the service role key\nexport function createDatabaseClient() {\n  const url = process.env.NEXT_PUBLIC_SUPABASE_URL\n  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY // Assuming this env var name\n\n  if (!url || !serviceRoleKey) {\n    throw new Error('Missing Supabase environment variables for database client.')\n  }\n\n  // Create a client with the service role key\n  return createAdminClient<Database>(\n    url,\n    serviceRoleKey,\n    {\n      auth: {\n        persistSession: false, // No need to persist session for read operations\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA,gUAA0E,6BAA6B;;;;AAGhG,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM;IACN,MAAM;IAEN,uCAAkB;;IAElB;IAEA,8EAA8E;IAC9E,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,KACA,KACA;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ;AAGO,SAAS;IACd,MAAM;IACN,MAAM,iBAAiB,QAAQ,GAAG,CAAC,yBAAyB,CAAC,6BAA6B;;IAE1F,IAAI,CAAC,OAAO,CAAC,gBAAgB;QAC3B,MAAM,IAAI,MAAM;IAClB;IAEA,4CAA4C;IAC5C,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAiB,AAAD,EACrB,KACA,gBACA;QACE,MAAM;YACJ,gBAAgB;QAClB;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Desktop/web/kaleidonex/src/app/api/track-visitor/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@/lib/supabase/server'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const {\n      path,\n      userAgent,\n      referrer,\n      screenResolution,\n      language,\n      timezone,\n      sessionId,\n      deviceType,\n      browser,\n      os,\n      country,\n      city,\n      pageTitle,\n      loadTime,\n      connectionType\n    } = await request.json()\n\n    // Get IP address from request headers\n    const forwarded = request.headers.get('x-forwarded-for')\n    const realIp = request.headers.get('x-real-ip')\n    const ip = forwarded?.split(',')[0] || realIp || request.ip || 'unknown'\n\n    // Get additional headers\n    const acceptLanguage = request.headers.get('accept-language')\n    const refererHeader = request.headers.get('referer')\n\n    const supabase = await createClient()\n\n    // Try to insert with enhanced schema first, fallback to basic schema if it fails\n    let insertData = {\n      ip_address: ip,\n      path,\n      user_agent: userAgent,\n      referrer: referrer || refererHeader,\n      screen_resolution: screenResolution,\n      language: language || acceptLanguage?.split(',')[0],\n      timezone,\n      session_id: sessionId,\n      device_type: deviceType,\n      browser,\n      operating_system: os,\n      country,\n      city,\n      page_title: pageTitle,\n      load_time: loadTime,\n      connection_type: connectionType,\n    }\n\n    let { error } = await supabase\n      .from('visitor_logs')\n      .insert(insertData)\n\n    // If enhanced schema fails, try with basic schema\n    if (error && error.code === 'PGRST204') {\n      console.log('Enhanced schema not available, using basic schema')\n      insertData = {\n        ip_address: ip,\n        path,\n        user_agent: userAgent,\n      }\n\n      const { error: basicError } = await supabase\n        .from('visitor_logs')\n        .insert(insertData)\n\n      error = basicError\n    }\n\n    if (error) {\n      console.error('Error tracking visitor:', error)\n      return NextResponse.json(\n        { error: 'Failed to track visitor' },\n        { status: 500 }\n      )\n    }\n\n    return NextResponse.json({ success: true })\n  } catch (error) {\n    console.error('Error in visitor tracking:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EACJ,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,gBAAgB,EAChB,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,UAAU,EACV,OAAO,EACP,EAAE,EACF,OAAO,EACP,IAAI,EACJ,SAAS,EACT,QAAQ,EACR,cAAc,EACf,GAAG,MAAM,QAAQ,IAAI;QAEtB,sCAAsC;QACtC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;QACtC,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,KAAK,WAAW,MAAM,IAAI,CAAC,EAAE,IAAI,UAAU,QAAQ,EAAE,IAAI;QAE/D,yBAAyB;QACzB,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC;QAC3C,MAAM,gBAAgB,QAAQ,OAAO,CAAC,GAAG,CAAC;QAE1C,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAElC,iFAAiF;QACjF,IAAI,aAAa;YACf,YAAY;YACZ;YACA,YAAY;YACZ,UAAU,YAAY;YACtB,mBAAmB;YACnB,UAAU,YAAY,gBAAgB,MAAM,IAAI,CAAC,EAAE;YACnD;YACA,YAAY;YACZ,aAAa;YACb;YACA,kBAAkB;YAClB;YACA;YACA,YAAY;YACZ,WAAW;YACX,iBAAiB;QACnB;QAEA,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SACnB,IAAI,CAAC,gBACL,MAAM,CAAC;QAEV,kDAAkD;QAClD,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY;YACtC,QAAQ,GAAG,CAAC;YACZ,aAAa;gBACX,YAAY;gBACZ;gBACA,YAAY;YACd;YAEA,MAAM,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,gBACL,MAAM,CAAC;YAEV,QAAQ;QACV;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}